package main

import (
	"github.com/gofiber/fiber/v2"
	"log"
	"payAPI/internal/config"
	"payAPI/internal/handler"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"payAPI/internal/websocket"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志系统
	logx.MustSetup(&cfg.Log)
	defer logx.Sync()

	// 创建服务上下文
	ctx, err := svc.NewServiceContext(cfg)
	if err != nil {
		logx.Error("Failed to create service context %v", err.Error())
		return
	}



	// 初始化WebSocket服务器
	websocket.InitWebSocketServer()
	logx.Info("WebSocket server initialized")

	// 创建 Fiber 应用
	app := fiber.New(fiber.Config{
		ErrorHandler: handler.ErrorHandler,
	})

	// 注册路由和处理器
	handler.RegisterHandlers(app, ctx)

	// 启动服务器
	logx.Info("Server starting", logx.String("port", cfg.Server.Port))
	if err := app.Listen(":" + cfg.Server.Port); err != nil {
		logx.Error("Failed to start server %v", err.Error())
		return
	}
}
