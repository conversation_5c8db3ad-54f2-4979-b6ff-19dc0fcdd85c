package main

import (
	"go.uber.org/fx"
	"go.uber.org/zap"
	"gorm.io/gen"
	"gorm.io/gorm"
	"log"
	"payAPI/internal/config"
	"payAPI/internal/svc"
)

func provideConfig() config.Config {
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	return *cfg
}

func provideDBClient(cfg config.Config) (*gorm.DB, error) {
	return svc.CreteDbClient(cfg.PostgresSQLConf)
}

func main() {
	var DB *gorm.DB

	app := fx.New(
		fx.Provide(zap.NewDevelopment),
		fx.Provide(provideConfig),
		fx.Provide(provideDBClient),
		fx.Populate(&DB),
	)

	if err := app.Err(); err != nil {
		panic(err)
	}

	g := gen.NewGenerator(gen.Config{
		Mode:              gen.WithoutContext | gen.WithDefaultQuery,
		OutPath:           "./dal",
		ModelPkgPath:      "./model/entity",
		FieldNullable:     true,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
	})

	g.UseDB(DB)

	// 获取所有表名
	tables, err := DB.Migrator().GetTables()
	if err != nil {
		panic(err)
	}

	// 为每个表生成模型，并设置 deleted_at 字段类型为 gorm.DeletedAt
	var models []interface{}
	for _, table := range tables {
		log.Println("正在生成模型:", table)
		models = append(models, g.GenerateModel(table, gen.FieldType("deleted_at", "gorm.DeletedAt")))
	}

	g.ApplyBasic(models...)

	g.Execute()
}
