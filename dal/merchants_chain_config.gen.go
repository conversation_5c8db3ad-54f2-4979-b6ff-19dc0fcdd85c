// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newMerchantsChainConfig(db *gorm.DB, opts ...gen.DOOption) merchantsChainConfig {
	_merchantsChainConfig := merchantsChainConfig{}

	_merchantsChainConfig.merchantsChainConfigDo.UseDB(db, opts...)
	_merchantsChainConfig.merchantsChainConfigDo.UseModel(&entity.MerchantsChainConfig{})

	tableName := _merchantsChainConfig.merchantsChainConfigDo.TableName()
	_merchantsChainConfig.ALL = field.NewAsterisk(tableName)
	_merchantsChainConfig.ID = field.NewInt64(tableName, "id")
	_merchantsChainConfig.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_merchantsChainConfig.Fee = field.NewFloat64(tableName, "fee")
	_merchantsChainConfig.AgentFee = field.NewFloat64(tableName, "agent_fee")
	_merchantsChainConfig.IsActive = field.NewBool(tableName, "is_active")
	_merchantsChainConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_merchantsChainConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_merchantsChainConfig.Ratetype = field.NewFloat64(tableName, "ratetype")
	_merchantsChainConfig.ChainName = field.NewString(tableName, "chain_name")

	_merchantsChainConfig.fillFieldMap()

	return _merchantsChainConfig
}

type merchantsChainConfig struct {
	merchantsChainConfigDo

	ALL         field.Asterisk
	ID          field.Int64
	MerchantsID field.Int64   // 商户ID
	Fee         field.Float64 // 链费率
	AgentFee    field.Float64 // 代理费率
	IsActive    field.Bool    // 是否启用该链配置
	CreatedAt   field.Time    // 创建时间
	UpdatedAt   field.Time    // 更新时间
	Ratetype    field.Float64 // 汇率类型 1固定 2浮动
	ChainName   field.String  // 区块链ID

	fieldMap map[string]field.Expr
}

func (m merchantsChainConfig) Table(newTableName string) *merchantsChainConfig {
	m.merchantsChainConfigDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchantsChainConfig) As(alias string) *merchantsChainConfig {
	m.merchantsChainConfigDo.DO = *(m.merchantsChainConfigDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchantsChainConfig) updateTableName(table string) *merchantsChainConfig {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.MerchantsID = field.NewInt64(table, "merchants_id")
	m.Fee = field.NewFloat64(table, "fee")
	m.AgentFee = field.NewFloat64(table, "agent_fee")
	m.IsActive = field.NewBool(table, "is_active")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.Ratetype = field.NewFloat64(table, "ratetype")
	m.ChainName = field.NewString(table, "chain_name")

	m.fillFieldMap()

	return m
}

func (m *merchantsChainConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchantsChainConfig) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["merchants_id"] = m.MerchantsID
	m.fieldMap["fee"] = m.Fee
	m.fieldMap["agent_fee"] = m.AgentFee
	m.fieldMap["is_active"] = m.IsActive
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["ratetype"] = m.Ratetype
	m.fieldMap["chain_name"] = m.ChainName
}

func (m merchantsChainConfig) clone(db *gorm.DB) merchantsChainConfig {
	m.merchantsChainConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchantsChainConfig) replaceDB(db *gorm.DB) merchantsChainConfig {
	m.merchantsChainConfigDo.ReplaceDB(db)
	return m
}

type merchantsChainConfigDo struct{ gen.DO }

func (m merchantsChainConfigDo) Debug() *merchantsChainConfigDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantsChainConfigDo) WithContext(ctx context.Context) *merchantsChainConfigDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantsChainConfigDo) ReadDB() *merchantsChainConfigDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantsChainConfigDo) WriteDB() *merchantsChainConfigDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantsChainConfigDo) Session(config *gorm.Session) *merchantsChainConfigDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantsChainConfigDo) Clauses(conds ...clause.Expression) *merchantsChainConfigDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantsChainConfigDo) Returning(value interface{}, columns ...string) *merchantsChainConfigDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantsChainConfigDo) Not(conds ...gen.Condition) *merchantsChainConfigDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantsChainConfigDo) Or(conds ...gen.Condition) *merchantsChainConfigDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantsChainConfigDo) Select(conds ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantsChainConfigDo) Where(conds ...gen.Condition) *merchantsChainConfigDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantsChainConfigDo) Order(conds ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantsChainConfigDo) Distinct(cols ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantsChainConfigDo) Omit(cols ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantsChainConfigDo) Join(table schema.Tabler, on ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantsChainConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantsChainConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantsChainConfigDo) Group(cols ...field.Expr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantsChainConfigDo) Having(conds ...gen.Condition) *merchantsChainConfigDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantsChainConfigDo) Limit(limit int) *merchantsChainConfigDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantsChainConfigDo) Offset(offset int) *merchantsChainConfigDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantsChainConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *merchantsChainConfigDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantsChainConfigDo) Unscoped() *merchantsChainConfigDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantsChainConfigDo) Create(values ...*entity.MerchantsChainConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantsChainConfigDo) CreateInBatches(values []*entity.MerchantsChainConfig, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantsChainConfigDo) Save(values ...*entity.MerchantsChainConfig) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantsChainConfigDo) First() (*entity.MerchantsChainConfig, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainConfig), nil
	}
}

func (m merchantsChainConfigDo) Take() (*entity.MerchantsChainConfig, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainConfig), nil
	}
}

func (m merchantsChainConfigDo) Last() (*entity.MerchantsChainConfig, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainConfig), nil
	}
}

func (m merchantsChainConfigDo) Find() ([]*entity.MerchantsChainConfig, error) {
	result, err := m.DO.Find()
	return result.([]*entity.MerchantsChainConfig), err
}

func (m merchantsChainConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.MerchantsChainConfig, err error) {
	buf := make([]*entity.MerchantsChainConfig, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantsChainConfigDo) FindInBatches(result *[]*entity.MerchantsChainConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantsChainConfigDo) Attrs(attrs ...field.AssignExpr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantsChainConfigDo) Assign(attrs ...field.AssignExpr) *merchantsChainConfigDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantsChainConfigDo) Joins(fields ...field.RelationField) *merchantsChainConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantsChainConfigDo) Preload(fields ...field.RelationField) *merchantsChainConfigDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantsChainConfigDo) FirstOrInit() (*entity.MerchantsChainConfig, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainConfig), nil
	}
}

func (m merchantsChainConfigDo) FirstOrCreate() (*entity.MerchantsChainConfig, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainConfig), nil
	}
}

func (m merchantsChainConfigDo) FindByPage(offset int, limit int) (result []*entity.MerchantsChainConfig, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantsChainConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantsChainConfigDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantsChainConfigDo) Delete(models ...*entity.MerchantsChainConfig) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantsChainConfigDo) withDO(do gen.Dao) *merchantsChainConfigDo {
	m.DO = *do.(*gen.DO)
	return m
}
