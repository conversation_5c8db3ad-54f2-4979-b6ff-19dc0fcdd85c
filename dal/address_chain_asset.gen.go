// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newAddressChainAsset(db *gorm.DB, opts ...gen.DOOption) addressChainAsset {
	_addressChainAsset := addressChainAsset{}

	_addressChainAsset.addressChainAssetDo.UseDB(db, opts...)
	_addressChainAsset.addressChainAssetDo.UseModel(&entity.AddressChainAsset{})

	tableName := _addressChainAsset.addressChainAssetDo.TableName()
	_addressChainAsset.ALL = field.NewAsterisk(tableName)
	_addressChainAsset.ID = field.NewInt64(tableName, "id")
	_addressChainAsset.AddressCoreID = field.NewInt64(tableName, "address_core_id")
	_addressChainAsset.ChainName = field.NewString(tableName, "chain_name")
	_addressChainAsset.Address = field.NewString(tableName, "address")
	_addressChainAsset.NativeBalance = field.NewFloat64(tableName, "native_balance")
	_addressChainAsset.UsdtBalance = field.NewFloat64(tableName, "usdt_balance")
	_addressChainAsset.TransactionsNums = field.NewInt32(tableName, "transactions_nums")
	_addressChainAsset.LockStatus = field.NewBool(tableName, "lock_status")
	_addressChainAsset.Status = field.NewInt32(tableName, "status")
	_addressChainAsset.CollectHash = field.NewString(tableName, "collect_hash")
	_addressChainAsset.Sid = field.NewInt64(tableName, "sid")
	_addressChainAsset.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_addressChainAsset.CreatedAt = field.NewTime(tableName, "created_at")
	_addressChainAsset.UpdatedAt = field.NewTime(tableName, "updated_at")
	_addressChainAsset.DeletedAt = field.NewField(tableName, "deleted_at")
	_addressChainAsset.EnergyBalance = field.NewInt64(tableName, "energy_balance")
	_addressChainAsset.EnergyUpdatedAt = field.NewTime(tableName, "energy_updated_at")
	_addressChainAsset.BandwidthBalance = field.NewInt64(tableName, "bandwidth_balance")
	_addressChainAsset.BandwidthUpdatedAt = field.NewTime(tableName, "bandwidth_updated_at")

	_addressChainAsset.fillFieldMap()

	return _addressChainAsset
}

type addressChainAsset struct {
	addressChainAssetDo

	ALL                field.Asterisk
	ID                 field.Int64
	AddressCoreID      field.Int64   // 核心地址ID
	ChainName          field.String  // 链类型名称
	Address            field.String  // 该链的地址
	NativeBalance      field.Float64 // 主币余额(TRX/ETH/BNB)
	UsdtBalance        field.Float64 // USDT余额
	TransactionsNums   field.Int32   // 交易笔数
	LockStatus         field.Bool    // true 锁定  false 未锁定
	Status             field.Int32   // 0正常  1不正常
	CollectHash        field.String  // 归集交易哈希
	Sid                field.Int64   // 归集任务序号
	MerchantsID        field.Int64   // 商户ID
	CreatedAt          field.Time
	UpdatedAt          field.Time
	DeletedAt          field.Field
	EnergyBalance      field.Int64
	EnergyUpdatedAt    field.Time
	BandwidthBalance   field.Int64
	BandwidthUpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (a addressChainAsset) Table(newTableName string) *addressChainAsset {
	a.addressChainAssetDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a addressChainAsset) As(alias string) *addressChainAsset {
	a.addressChainAssetDo.DO = *(a.addressChainAssetDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *addressChainAsset) updateTableName(table string) *addressChainAsset {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AddressCoreID = field.NewInt64(table, "address_core_id")
	a.ChainName = field.NewString(table, "chain_name")
	a.Address = field.NewString(table, "address")
	a.NativeBalance = field.NewFloat64(table, "native_balance")
	a.UsdtBalance = field.NewFloat64(table, "usdt_balance")
	a.TransactionsNums = field.NewInt32(table, "transactions_nums")
	a.LockStatus = field.NewBool(table, "lock_status")
	a.Status = field.NewInt32(table, "status")
	a.CollectHash = field.NewString(table, "collect_hash")
	a.Sid = field.NewInt64(table, "sid")
	a.MerchantsID = field.NewInt64(table, "merchants_id")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")
	a.EnergyBalance = field.NewInt64(table, "energy_balance")
	a.EnergyUpdatedAt = field.NewTime(table, "energy_updated_at")
	a.BandwidthBalance = field.NewInt64(table, "bandwidth_balance")
	a.BandwidthUpdatedAt = field.NewTime(table, "bandwidth_updated_at")

	a.fillFieldMap()

	return a
}

func (a *addressChainAsset) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *addressChainAsset) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 19)
	a.fieldMap["id"] = a.ID
	a.fieldMap["address_core_id"] = a.AddressCoreID
	a.fieldMap["chain_name"] = a.ChainName
	a.fieldMap["address"] = a.Address
	a.fieldMap["native_balance"] = a.NativeBalance
	a.fieldMap["usdt_balance"] = a.UsdtBalance
	a.fieldMap["transactions_nums"] = a.TransactionsNums
	a.fieldMap["lock_status"] = a.LockStatus
	a.fieldMap["status"] = a.Status
	a.fieldMap["collect_hash"] = a.CollectHash
	a.fieldMap["sid"] = a.Sid
	a.fieldMap["merchants_id"] = a.MerchantsID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["energy_balance"] = a.EnergyBalance
	a.fieldMap["energy_updated_at"] = a.EnergyUpdatedAt
	a.fieldMap["bandwidth_balance"] = a.BandwidthBalance
	a.fieldMap["bandwidth_updated_at"] = a.BandwidthUpdatedAt
}

func (a addressChainAsset) clone(db *gorm.DB) addressChainAsset {
	a.addressChainAssetDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a addressChainAsset) replaceDB(db *gorm.DB) addressChainAsset {
	a.addressChainAssetDo.ReplaceDB(db)
	return a
}

type addressChainAssetDo struct{ gen.DO }

func (a addressChainAssetDo) Debug() *addressChainAssetDo {
	return a.withDO(a.DO.Debug())
}

func (a addressChainAssetDo) WithContext(ctx context.Context) *addressChainAssetDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a addressChainAssetDo) ReadDB() *addressChainAssetDo {
	return a.Clauses(dbresolver.Read)
}

func (a addressChainAssetDo) WriteDB() *addressChainAssetDo {
	return a.Clauses(dbresolver.Write)
}

func (a addressChainAssetDo) Session(config *gorm.Session) *addressChainAssetDo {
	return a.withDO(a.DO.Session(config))
}

func (a addressChainAssetDo) Clauses(conds ...clause.Expression) *addressChainAssetDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a addressChainAssetDo) Returning(value interface{}, columns ...string) *addressChainAssetDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a addressChainAssetDo) Not(conds ...gen.Condition) *addressChainAssetDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a addressChainAssetDo) Or(conds ...gen.Condition) *addressChainAssetDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a addressChainAssetDo) Select(conds ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a addressChainAssetDo) Where(conds ...gen.Condition) *addressChainAssetDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a addressChainAssetDo) Order(conds ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a addressChainAssetDo) Distinct(cols ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a addressChainAssetDo) Omit(cols ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a addressChainAssetDo) Join(table schema.Tabler, on ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a addressChainAssetDo) LeftJoin(table schema.Tabler, on ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a addressChainAssetDo) RightJoin(table schema.Tabler, on ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a addressChainAssetDo) Group(cols ...field.Expr) *addressChainAssetDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a addressChainAssetDo) Having(conds ...gen.Condition) *addressChainAssetDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a addressChainAssetDo) Limit(limit int) *addressChainAssetDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a addressChainAssetDo) Offset(offset int) *addressChainAssetDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a addressChainAssetDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *addressChainAssetDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a addressChainAssetDo) Unscoped() *addressChainAssetDo {
	return a.withDO(a.DO.Unscoped())
}

func (a addressChainAssetDo) Create(values ...*entity.AddressChainAsset) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a addressChainAssetDo) CreateInBatches(values []*entity.AddressChainAsset, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a addressChainAssetDo) Save(values ...*entity.AddressChainAsset) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a addressChainAssetDo) First() (*entity.AddressChainAsset, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressChainAsset), nil
	}
}

func (a addressChainAssetDo) Take() (*entity.AddressChainAsset, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressChainAsset), nil
	}
}

func (a addressChainAssetDo) Last() (*entity.AddressChainAsset, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressChainAsset), nil
	}
}

func (a addressChainAssetDo) Find() ([]*entity.AddressChainAsset, error) {
	result, err := a.DO.Find()
	return result.([]*entity.AddressChainAsset), err
}

func (a addressChainAssetDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.AddressChainAsset, err error) {
	buf := make([]*entity.AddressChainAsset, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a addressChainAssetDo) FindInBatches(result *[]*entity.AddressChainAsset, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a addressChainAssetDo) Attrs(attrs ...field.AssignExpr) *addressChainAssetDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a addressChainAssetDo) Assign(attrs ...field.AssignExpr) *addressChainAssetDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a addressChainAssetDo) Joins(fields ...field.RelationField) *addressChainAssetDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a addressChainAssetDo) Preload(fields ...field.RelationField) *addressChainAssetDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a addressChainAssetDo) FirstOrInit() (*entity.AddressChainAsset, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressChainAsset), nil
	}
}

func (a addressChainAssetDo) FirstOrCreate() (*entity.AddressChainAsset, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressChainAsset), nil
	}
}

func (a addressChainAssetDo) FindByPage(offset int, limit int) (result []*entity.AddressChainAsset, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a addressChainAssetDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a addressChainAssetDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a addressChainAssetDo) Delete(models ...*entity.AddressChainAsset) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *addressChainAssetDo) withDO(do gen.Dao) *addressChainAssetDo {
	a.DO = *do.(*gen.DO)
	return a
}
