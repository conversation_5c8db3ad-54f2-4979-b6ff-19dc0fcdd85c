// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newRentalEnergyAddressConfig(db *gorm.DB, opts ...gen.DOOption) rentalEnergyAddressConfig {
	_rentalEnergyAddressConfig := rentalEnergyAddressConfig{}

	_rentalEnergyAddressConfig.rentalEnergyAddressConfigDo.UseDB(db, opts...)
	_rentalEnergyAddressConfig.rentalEnergyAddressConfigDo.UseModel(&entity.RentalEnergyAddressConfig{})

	tableName := _rentalEnergyAddressConfig.rentalEnergyAddressConfigDo.TableName()
	_rentalEnergyAddressConfig.ALL = field.NewAsterisk(tableName)
	_rentalEnergyAddressConfig.ID = field.NewInt64(tableName, "id")
	_rentalEnergyAddressConfig.Address = field.NewString(tableName, "address")
	_rentalEnergyAddressConfig.Amount = field.NewFloat64(tableName, "amount")
	_rentalEnergyAddressConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_rentalEnergyAddressConfig.UpdatedAt = field.NewTime(tableName, "updated_at")

	_rentalEnergyAddressConfig.fillFieldMap()

	return _rentalEnergyAddressConfig
}

type rentalEnergyAddressConfig struct {
	rentalEnergyAddressConfigDo

	ALL       field.Asterisk
	ID        field.Int64
	Address   field.String
	Amount    field.Float64
	CreatedAt field.Time // 创建时间
	UpdatedAt field.Time // 更新时间

	fieldMap map[string]field.Expr
}

func (r rentalEnergyAddressConfig) Table(newTableName string) *rentalEnergyAddressConfig {
	r.rentalEnergyAddressConfigDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r rentalEnergyAddressConfig) As(alias string) *rentalEnergyAddressConfig {
	r.rentalEnergyAddressConfigDo.DO = *(r.rentalEnergyAddressConfigDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *rentalEnergyAddressConfig) updateTableName(table string) *rentalEnergyAddressConfig {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.Address = field.NewString(table, "address")
	r.Amount = field.NewFloat64(table, "amount")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedAt = field.NewTime(table, "updated_at")

	r.fillFieldMap()

	return r
}

func (r *rentalEnergyAddressConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *rentalEnergyAddressConfig) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 5)
	r.fieldMap["id"] = r.ID
	r.fieldMap["address"] = r.Address
	r.fieldMap["amount"] = r.Amount
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
}

func (r rentalEnergyAddressConfig) clone(db *gorm.DB) rentalEnergyAddressConfig {
	r.rentalEnergyAddressConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r rentalEnergyAddressConfig) replaceDB(db *gorm.DB) rentalEnergyAddressConfig {
	r.rentalEnergyAddressConfigDo.ReplaceDB(db)
	return r
}

type rentalEnergyAddressConfigDo struct{ gen.DO }

func (r rentalEnergyAddressConfigDo) Debug() *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Debug())
}

func (r rentalEnergyAddressConfigDo) WithContext(ctx context.Context) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r rentalEnergyAddressConfigDo) ReadDB() *rentalEnergyAddressConfigDo {
	return r.Clauses(dbresolver.Read)
}

func (r rentalEnergyAddressConfigDo) WriteDB() *rentalEnergyAddressConfigDo {
	return r.Clauses(dbresolver.Write)
}

func (r rentalEnergyAddressConfigDo) Session(config *gorm.Session) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Session(config))
}

func (r rentalEnergyAddressConfigDo) Clauses(conds ...clause.Expression) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r rentalEnergyAddressConfigDo) Returning(value interface{}, columns ...string) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r rentalEnergyAddressConfigDo) Not(conds ...gen.Condition) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r rentalEnergyAddressConfigDo) Or(conds ...gen.Condition) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r rentalEnergyAddressConfigDo) Select(conds ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r rentalEnergyAddressConfigDo) Where(conds ...gen.Condition) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r rentalEnergyAddressConfigDo) Order(conds ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r rentalEnergyAddressConfigDo) Distinct(cols ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r rentalEnergyAddressConfigDo) Omit(cols ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r rentalEnergyAddressConfigDo) Join(table schema.Tabler, on ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r rentalEnergyAddressConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r rentalEnergyAddressConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r rentalEnergyAddressConfigDo) Group(cols ...field.Expr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r rentalEnergyAddressConfigDo) Having(conds ...gen.Condition) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r rentalEnergyAddressConfigDo) Limit(limit int) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r rentalEnergyAddressConfigDo) Offset(offset int) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r rentalEnergyAddressConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r rentalEnergyAddressConfigDo) Unscoped() *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Unscoped())
}

func (r rentalEnergyAddressConfigDo) Create(values ...*entity.RentalEnergyAddressConfig) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r rentalEnergyAddressConfigDo) CreateInBatches(values []*entity.RentalEnergyAddressConfig, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r rentalEnergyAddressConfigDo) Save(values ...*entity.RentalEnergyAddressConfig) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r rentalEnergyAddressConfigDo) First() (*entity.RentalEnergyAddressConfig, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RentalEnergyAddressConfig), nil
	}
}

func (r rentalEnergyAddressConfigDo) Take() (*entity.RentalEnergyAddressConfig, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RentalEnergyAddressConfig), nil
	}
}

func (r rentalEnergyAddressConfigDo) Last() (*entity.RentalEnergyAddressConfig, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RentalEnergyAddressConfig), nil
	}
}

func (r rentalEnergyAddressConfigDo) Find() ([]*entity.RentalEnergyAddressConfig, error) {
	result, err := r.DO.Find()
	return result.([]*entity.RentalEnergyAddressConfig), err
}

func (r rentalEnergyAddressConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.RentalEnergyAddressConfig, err error) {
	buf := make([]*entity.RentalEnergyAddressConfig, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r rentalEnergyAddressConfigDo) FindInBatches(result *[]*entity.RentalEnergyAddressConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r rentalEnergyAddressConfigDo) Attrs(attrs ...field.AssignExpr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r rentalEnergyAddressConfigDo) Assign(attrs ...field.AssignExpr) *rentalEnergyAddressConfigDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r rentalEnergyAddressConfigDo) Joins(fields ...field.RelationField) *rentalEnergyAddressConfigDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r rentalEnergyAddressConfigDo) Preload(fields ...field.RelationField) *rentalEnergyAddressConfigDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r rentalEnergyAddressConfigDo) FirstOrInit() (*entity.RentalEnergyAddressConfig, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RentalEnergyAddressConfig), nil
	}
}

func (r rentalEnergyAddressConfigDo) FirstOrCreate() (*entity.RentalEnergyAddressConfig, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.RentalEnergyAddressConfig), nil
	}
}

func (r rentalEnergyAddressConfigDo) FindByPage(offset int, limit int) (result []*entity.RentalEnergyAddressConfig, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r rentalEnergyAddressConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r rentalEnergyAddressConfigDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r rentalEnergyAddressConfigDo) Delete(models ...*entity.RentalEnergyAddressConfig) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *rentalEnergyAddressConfigDo) withDO(do gen.Dao) *rentalEnergyAddressConfigDo {
	r.DO = *do.(*gen.DO)
	return r
}
