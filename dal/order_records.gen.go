// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newOrderRecords(db *gorm.DB, opts ...gen.DOOption) orderRecords {
	_orderRecords := orderRecords{}

	_orderRecords.orderRecordsDo.UseDB(db, opts...)
	_orderRecords.orderRecordsDo.UseModel(&entity.OrderRecords{})

	tableName := _orderRecords.orderRecordsDo.TableName()
	_orderRecords.ALL = field.NewAsterisk(tableName)
	_orderRecords.ID = field.NewString(tableName, "id")
	_orderRecords.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_orderRecords.ChainName = field.NewString(tableName, "chain_name")
	_orderRecords.Type = field.NewInt32(tableName, "type")
	_orderRecords.Amount = field.NewFloat64(tableName, "amount")
	_orderRecords.RorderID = field.NewString(tableName, "rorder_id")
	_orderRecords.TransactionID = field.NewString(tableName, "transaction_id")
	_orderRecords.Remark = field.NewString(tableName, "remark")
	_orderRecords.Status = field.NewInt32(tableName, "status")
	_orderRecords.CreatedAt = field.NewTime(tableName, "created_at")
	_orderRecords.UpdatedAt = field.NewTime(tableName, "updated_at")
	_orderRecords.DeletedAt = field.NewField(tableName, "deleted_at")

	_orderRecords.fillFieldMap()

	return _orderRecords
}

type orderRecords struct {
	orderRecordsDo

	ALL           field.Asterisk
	ID            field.String
	MerchantsID   field.Int64 // 商户id
	ChainName     field.String
	Type          field.Int32   // 1 商户 2代理
	Amount        field.Float64 // 金额
	RorderID      field.String  // 商户订单id
	TransactionID field.String  // 交易id  hash
	Remark        field.String  // 备注
	Status        field.Int32   // 0 禁用  1正常
	CreatedAt     field.Time    // 创建时间
	UpdatedAt     field.Time    // 更新时间
	DeletedAt     field.Field

	fieldMap map[string]field.Expr
}

func (o orderRecords) Table(newTableName string) *orderRecords {
	o.orderRecordsDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o orderRecords) As(alias string) *orderRecords {
	o.orderRecordsDo.DO = *(o.orderRecordsDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *orderRecords) updateTableName(table string) *orderRecords {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewString(table, "id")
	o.MerchantsID = field.NewInt64(table, "merchants_id")
	o.ChainName = field.NewString(table, "chain_name")
	o.Type = field.NewInt32(table, "type")
	o.Amount = field.NewFloat64(table, "amount")
	o.RorderID = field.NewString(table, "rorder_id")
	o.TransactionID = field.NewString(table, "transaction_id")
	o.Remark = field.NewString(table, "remark")
	o.Status = field.NewInt32(table, "status")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.DeletedAt = field.NewField(table, "deleted_at")

	o.fillFieldMap()

	return o
}

func (o *orderRecords) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *orderRecords) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 12)
	o.fieldMap["id"] = o.ID
	o.fieldMap["merchants_id"] = o.MerchantsID
	o.fieldMap["chain_name"] = o.ChainName
	o.fieldMap["type"] = o.Type
	o.fieldMap["amount"] = o.Amount
	o.fieldMap["rorder_id"] = o.RorderID
	o.fieldMap["transaction_id"] = o.TransactionID
	o.fieldMap["remark"] = o.Remark
	o.fieldMap["status"] = o.Status
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
}

func (o orderRecords) clone(db *gorm.DB) orderRecords {
	o.orderRecordsDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o orderRecords) replaceDB(db *gorm.DB) orderRecords {
	o.orderRecordsDo.ReplaceDB(db)
	return o
}

type orderRecordsDo struct{ gen.DO }

func (o orderRecordsDo) Debug() *orderRecordsDo {
	return o.withDO(o.DO.Debug())
}

func (o orderRecordsDo) WithContext(ctx context.Context) *orderRecordsDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o orderRecordsDo) ReadDB() *orderRecordsDo {
	return o.Clauses(dbresolver.Read)
}

func (o orderRecordsDo) WriteDB() *orderRecordsDo {
	return o.Clauses(dbresolver.Write)
}

func (o orderRecordsDo) Session(config *gorm.Session) *orderRecordsDo {
	return o.withDO(o.DO.Session(config))
}

func (o orderRecordsDo) Clauses(conds ...clause.Expression) *orderRecordsDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o orderRecordsDo) Returning(value interface{}, columns ...string) *orderRecordsDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o orderRecordsDo) Not(conds ...gen.Condition) *orderRecordsDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o orderRecordsDo) Or(conds ...gen.Condition) *orderRecordsDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o orderRecordsDo) Select(conds ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o orderRecordsDo) Where(conds ...gen.Condition) *orderRecordsDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o orderRecordsDo) Order(conds ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o orderRecordsDo) Distinct(cols ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o orderRecordsDo) Omit(cols ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o orderRecordsDo) Join(table schema.Tabler, on ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o orderRecordsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o orderRecordsDo) RightJoin(table schema.Tabler, on ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o orderRecordsDo) Group(cols ...field.Expr) *orderRecordsDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o orderRecordsDo) Having(conds ...gen.Condition) *orderRecordsDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o orderRecordsDo) Limit(limit int) *orderRecordsDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o orderRecordsDo) Offset(offset int) *orderRecordsDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o orderRecordsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *orderRecordsDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o orderRecordsDo) Unscoped() *orderRecordsDo {
	return o.withDO(o.DO.Unscoped())
}

func (o orderRecordsDo) Create(values ...*entity.OrderRecords) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o orderRecordsDo) CreateInBatches(values []*entity.OrderRecords, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o orderRecordsDo) Save(values ...*entity.OrderRecords) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o orderRecordsDo) First() (*entity.OrderRecords, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.OrderRecords), nil
	}
}

func (o orderRecordsDo) Take() (*entity.OrderRecords, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.OrderRecords), nil
	}
}

func (o orderRecordsDo) Last() (*entity.OrderRecords, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.OrderRecords), nil
	}
}

func (o orderRecordsDo) Find() ([]*entity.OrderRecords, error) {
	result, err := o.DO.Find()
	return result.([]*entity.OrderRecords), err
}

func (o orderRecordsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.OrderRecords, err error) {
	buf := make([]*entity.OrderRecords, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o orderRecordsDo) FindInBatches(result *[]*entity.OrderRecords, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o orderRecordsDo) Attrs(attrs ...field.AssignExpr) *orderRecordsDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o orderRecordsDo) Assign(attrs ...field.AssignExpr) *orderRecordsDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o orderRecordsDo) Joins(fields ...field.RelationField) *orderRecordsDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o orderRecordsDo) Preload(fields ...field.RelationField) *orderRecordsDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o orderRecordsDo) FirstOrInit() (*entity.OrderRecords, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.OrderRecords), nil
	}
}

func (o orderRecordsDo) FirstOrCreate() (*entity.OrderRecords, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.OrderRecords), nil
	}
}

func (o orderRecordsDo) FindByPage(offset int, limit int) (result []*entity.OrderRecords, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o orderRecordsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o orderRecordsDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o orderRecordsDo) Delete(models ...*entity.OrderRecords) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *orderRecordsDo) withDO(do gen.Dao) *orderRecordsDo {
	o.DO = *do.(*gen.DO)
	return o
}
