// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newWhiteList(db *gorm.DB, opts ...gen.DOOption) whiteList {
	_whiteList := whiteList{}

	_whiteList.whiteListDo.UseDB(db, opts...)
	_whiteList.whiteListDo.UseModel(&entity.WhiteList{})

	tableName := _whiteList.whiteListDo.TableName()
	_whiteList.ALL = field.NewAsterisk(tableName)
	_whiteList.ID = field.NewInt64(tableName, "id")
	_whiteList.IP = field.NewString(tableName, "ip")
	_whiteList.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_whiteList.Type = field.NewInt32(tableName, "type")
	_whiteList.Status = field.NewInt32(tableName, "status")
	_whiteList.CreatedAt = field.NewTime(tableName, "created_at")
	_whiteList.UpdatedAt = field.NewTime(tableName, "updated_at")
	_whiteList.DeletedAt = field.NewField(tableName, "deleted_at")

	_whiteList.fillFieldMap()

	return _whiteList
}

type whiteList struct {
	whiteListDo

	ALL         field.Asterisk
	ID          field.Int64
	IP          field.String // ip地址
	MerchantsID field.Int64  // 商户id
	Type        field.Int32  // 1 商户后台 2商户api
	Status      field.Int32  // 0 禁用 1正常
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field

	fieldMap map[string]field.Expr
}

func (w whiteList) Table(newTableName string) *whiteList {
	w.whiteListDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w whiteList) As(alias string) *whiteList {
	w.whiteListDo.DO = *(w.whiteListDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *whiteList) updateTableName(table string) *whiteList {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.IP = field.NewString(table, "ip")
	w.MerchantsID = field.NewInt64(table, "merchants_id")
	w.Type = field.NewInt32(table, "type")
	w.Status = field.NewInt32(table, "status")
	w.CreatedAt = field.NewTime(table, "created_at")
	w.UpdatedAt = field.NewTime(table, "updated_at")
	w.DeletedAt = field.NewField(table, "deleted_at")

	w.fillFieldMap()

	return w
}

func (w *whiteList) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *whiteList) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 8)
	w.fieldMap["id"] = w.ID
	w.fieldMap["ip"] = w.IP
	w.fieldMap["merchants_id"] = w.MerchantsID
	w.fieldMap["type"] = w.Type
	w.fieldMap["status"] = w.Status
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
}

func (w whiteList) clone(db *gorm.DB) whiteList {
	w.whiteListDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w whiteList) replaceDB(db *gorm.DB) whiteList {
	w.whiteListDo.ReplaceDB(db)
	return w
}

type whiteListDo struct{ gen.DO }

func (w whiteListDo) Debug() *whiteListDo {
	return w.withDO(w.DO.Debug())
}

func (w whiteListDo) WithContext(ctx context.Context) *whiteListDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w whiteListDo) ReadDB() *whiteListDo {
	return w.Clauses(dbresolver.Read)
}

func (w whiteListDo) WriteDB() *whiteListDo {
	return w.Clauses(dbresolver.Write)
}

func (w whiteListDo) Session(config *gorm.Session) *whiteListDo {
	return w.withDO(w.DO.Session(config))
}

func (w whiteListDo) Clauses(conds ...clause.Expression) *whiteListDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w whiteListDo) Returning(value interface{}, columns ...string) *whiteListDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w whiteListDo) Not(conds ...gen.Condition) *whiteListDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w whiteListDo) Or(conds ...gen.Condition) *whiteListDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w whiteListDo) Select(conds ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w whiteListDo) Where(conds ...gen.Condition) *whiteListDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w whiteListDo) Order(conds ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w whiteListDo) Distinct(cols ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w whiteListDo) Omit(cols ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w whiteListDo) Join(table schema.Tabler, on ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w whiteListDo) LeftJoin(table schema.Tabler, on ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w whiteListDo) RightJoin(table schema.Tabler, on ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w whiteListDo) Group(cols ...field.Expr) *whiteListDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w whiteListDo) Having(conds ...gen.Condition) *whiteListDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w whiteListDo) Limit(limit int) *whiteListDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w whiteListDo) Offset(offset int) *whiteListDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w whiteListDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *whiteListDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w whiteListDo) Unscoped() *whiteListDo {
	return w.withDO(w.DO.Unscoped())
}

func (w whiteListDo) Create(values ...*entity.WhiteList) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w whiteListDo) CreateInBatches(values []*entity.WhiteList, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w whiteListDo) Save(values ...*entity.WhiteList) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w whiteListDo) First() (*entity.WhiteList, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.WhiteList), nil
	}
}

func (w whiteListDo) Take() (*entity.WhiteList, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.WhiteList), nil
	}
}

func (w whiteListDo) Last() (*entity.WhiteList, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.WhiteList), nil
	}
}

func (w whiteListDo) Find() ([]*entity.WhiteList, error) {
	result, err := w.DO.Find()
	return result.([]*entity.WhiteList), err
}

func (w whiteListDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.WhiteList, err error) {
	buf := make([]*entity.WhiteList, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w whiteListDo) FindInBatches(result *[]*entity.WhiteList, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w whiteListDo) Attrs(attrs ...field.AssignExpr) *whiteListDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w whiteListDo) Assign(attrs ...field.AssignExpr) *whiteListDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w whiteListDo) Joins(fields ...field.RelationField) *whiteListDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w whiteListDo) Preload(fields ...field.RelationField) *whiteListDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w whiteListDo) FirstOrInit() (*entity.WhiteList, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.WhiteList), nil
	}
}

func (w whiteListDo) FirstOrCreate() (*entity.WhiteList, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.WhiteList), nil
	}
}

func (w whiteListDo) FindByPage(offset int, limit int) (result []*entity.WhiteList, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w whiteListDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w whiteListDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w whiteListDo) Delete(models ...*entity.WhiteList) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *whiteListDo) withDO(do gen.Dao) *whiteListDo {
	w.DO = *do.(*gen.DO)
	return w
}
