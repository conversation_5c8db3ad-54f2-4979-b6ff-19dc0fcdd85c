// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newLockAddress(db *gorm.DB, opts ...gen.DOOption) lockAddress {
	_lockAddress := lockAddress{}

	_lockAddress.lockAddressDo.UseDB(db, opts...)
	_lockAddress.lockAddressDo.UseModel(&entity.LockAddress{})

	tableName := _lockAddress.lockAddressDo.TableName()
	_lockAddress.ALL = field.NewAsterisk(tableName)
	_lockAddress.ID = field.NewInt64(tableName, "id")
	_lockAddress.AddressID = field.NewInt64(tableName, "address_id")
	_lockAddress.Amount = field.NewFloat64(tableName, "amount")
	_lockAddress.LockTime = field.NewTime(tableName, "lock_time")
	_lockAddress.UnlockTime = field.NewTime(tableName, "unlock_time")
	_lockAddress.Islock = field.NewBool(tableName, "islock")

	_lockAddress.fillFieldMap()

	return _lockAddress
}

type lockAddress struct {
	lockAddressDo

	ALL        field.Asterisk
	ID         field.Int64
	AddressID  field.Int64
	Amount     field.Float64
	LockTime   field.Time
	UnlockTime field.Time
	Islock     field.Bool

	fieldMap map[string]field.Expr
}

func (l lockAddress) Table(newTableName string) *lockAddress {
	l.lockAddressDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l lockAddress) As(alias string) *lockAddress {
	l.lockAddressDo.DO = *(l.lockAddressDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *lockAddress) updateTableName(table string) *lockAddress {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewInt64(table, "id")
	l.AddressID = field.NewInt64(table, "address_id")
	l.Amount = field.NewFloat64(table, "amount")
	l.LockTime = field.NewTime(table, "lock_time")
	l.UnlockTime = field.NewTime(table, "unlock_time")
	l.Islock = field.NewBool(table, "islock")

	l.fillFieldMap()

	return l
}

func (l *lockAddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *lockAddress) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 6)
	l.fieldMap["id"] = l.ID
	l.fieldMap["address_id"] = l.AddressID
	l.fieldMap["amount"] = l.Amount
	l.fieldMap["lock_time"] = l.LockTime
	l.fieldMap["unlock_time"] = l.UnlockTime
	l.fieldMap["islock"] = l.Islock
}

func (l lockAddress) clone(db *gorm.DB) lockAddress {
	l.lockAddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l lockAddress) replaceDB(db *gorm.DB) lockAddress {
	l.lockAddressDo.ReplaceDB(db)
	return l
}

type lockAddressDo struct{ gen.DO }

func (l lockAddressDo) Debug() *lockAddressDo {
	return l.withDO(l.DO.Debug())
}

func (l lockAddressDo) WithContext(ctx context.Context) *lockAddressDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l lockAddressDo) ReadDB() *lockAddressDo {
	return l.Clauses(dbresolver.Read)
}

func (l lockAddressDo) WriteDB() *lockAddressDo {
	return l.Clauses(dbresolver.Write)
}

func (l lockAddressDo) Session(config *gorm.Session) *lockAddressDo {
	return l.withDO(l.DO.Session(config))
}

func (l lockAddressDo) Clauses(conds ...clause.Expression) *lockAddressDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l lockAddressDo) Returning(value interface{}, columns ...string) *lockAddressDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l lockAddressDo) Not(conds ...gen.Condition) *lockAddressDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l lockAddressDo) Or(conds ...gen.Condition) *lockAddressDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l lockAddressDo) Select(conds ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l lockAddressDo) Where(conds ...gen.Condition) *lockAddressDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l lockAddressDo) Order(conds ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l lockAddressDo) Distinct(cols ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l lockAddressDo) Omit(cols ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l lockAddressDo) Join(table schema.Tabler, on ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l lockAddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l lockAddressDo) RightJoin(table schema.Tabler, on ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l lockAddressDo) Group(cols ...field.Expr) *lockAddressDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l lockAddressDo) Having(conds ...gen.Condition) *lockAddressDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l lockAddressDo) Limit(limit int) *lockAddressDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l lockAddressDo) Offset(offset int) *lockAddressDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l lockAddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *lockAddressDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l lockAddressDo) Unscoped() *lockAddressDo {
	return l.withDO(l.DO.Unscoped())
}

func (l lockAddressDo) Create(values ...*entity.LockAddress) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l lockAddressDo) CreateInBatches(values []*entity.LockAddress, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l lockAddressDo) Save(values ...*entity.LockAddress) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l lockAddressDo) First() (*entity.LockAddress, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.LockAddress), nil
	}
}

func (l lockAddressDo) Take() (*entity.LockAddress, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.LockAddress), nil
	}
}

func (l lockAddressDo) Last() (*entity.LockAddress, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.LockAddress), nil
	}
}

func (l lockAddressDo) Find() ([]*entity.LockAddress, error) {
	result, err := l.DO.Find()
	return result.([]*entity.LockAddress), err
}

func (l lockAddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.LockAddress, err error) {
	buf := make([]*entity.LockAddress, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l lockAddressDo) FindInBatches(result *[]*entity.LockAddress, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l lockAddressDo) Attrs(attrs ...field.AssignExpr) *lockAddressDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l lockAddressDo) Assign(attrs ...field.AssignExpr) *lockAddressDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l lockAddressDo) Joins(fields ...field.RelationField) *lockAddressDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l lockAddressDo) Preload(fields ...field.RelationField) *lockAddressDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l lockAddressDo) FirstOrInit() (*entity.LockAddress, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.LockAddress), nil
	}
}

func (l lockAddressDo) FirstOrCreate() (*entity.LockAddress, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.LockAddress), nil
	}
}

func (l lockAddressDo) FindByPage(offset int, limit int) (result []*entity.LockAddress, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l lockAddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l lockAddressDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l lockAddressDo) Delete(models ...*entity.LockAddress) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *lockAddressDo) withDO(do gen.Dao) *lockAddressDo {
	l.DO = *do.(*gen.DO)
	return l
}
