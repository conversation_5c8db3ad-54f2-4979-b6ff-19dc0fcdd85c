// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newOrders(db *gorm.DB, opts ...gen.DOOption) orders {
	_orders := orders{}

	_orders.ordersDo.UseDB(db, opts...)
	_orders.ordersDo.UseModel(&entity.Orders{})

	tableName := _orders.ordersDo.TableName()
	_orders.ALL = field.NewAsterisk(tableName)
	_orders.ID = field.NewInt64(tableName, "id")
	_orders.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_orders.TransactionID = field.NewString(tableName, "transaction_id")
	_orders.OrderID = field.NewString(tableName, "order_id")
	_orders.MerchantOrderID = field.NewString(tableName, "merchant_order_id")
	_orders.PayAddress = field.NewString(tableName, "pay_address")
	_orders.PayScore = field.NewString(tableName, "pay_score")
	_orders.Address = field.NewString(tableName, "address")
	_orders.AddressID = field.NewInt64(tableName, "address_id")
	_orders.Amount = field.NewFloat64(tableName, "amount")
	_orders.Status = field.NewInt32(tableName, "status")
	_orders.CallbackStatus = field.NewInt32(tableName, "callback_status")
	_orders.CallbackURL = field.NewString(tableName, "callback_url")
	_orders.CallbackNum = field.NewInt32(tableName, "callback_num")
	_orders.CallbackResult = field.NewString(tableName, "callback_result")
	_orders.Rate = field.NewFloat64(tableName, "rate")
	_orders.RealAmount = field.NewFloat64(tableName, "real_amount")
	_orders.ChainName = field.NewString(tableName, "chain_name")
	_orders.ReturnURL = field.NewString(tableName, "return_url")
	_orders.CreatedAt = field.NewTime(tableName, "created_at")
	_orders.UpdatedAt = field.NewTime(tableName, "updated_at")
	_orders.DeletedAt = field.NewField(tableName, "deleted_at")

	_orders.fillFieldMap()

	return _orders
}

type orders struct {
	ordersDo

	ALL             field.Asterisk
	ID              field.Int64
	MerchantsID     field.Int64
	TransactionID   field.String
	OrderID         field.String  // 订单号
	MerchantOrderID field.String  // 商户订单号
	PayAddress      field.String  // 付款地址
	PayScore        field.String  // 付款地址风险评分
	Address         field.String  // 地址
	AddressID       field.Int64   // 地址
	Amount          field.Float64 // 金额
	Status          field.Int32   // 0:未支付 1:支付成功  2:主动成功 3:订单超时
	CallbackStatus  field.Int32   // 0 未回调  1 回调成功  1回调失败
	CallbackURL     field.String  // 回调地址
	CallbackNum     field.Int32   // 回调次数
	CallbackResult  field.String  // 回调返回信息
	Rate            field.Float64 // 汇率
	RealAmount      field.Float64 // 实收金额
	ChainName       field.String  // 链id
	ReturnURL       field.String  // 跳转地址
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field

	fieldMap map[string]field.Expr
}

func (o orders) Table(newTableName string) *orders {
	o.ordersDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o orders) As(alias string) *orders {
	o.ordersDo.DO = *(o.ordersDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *orders) updateTableName(table string) *orders {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewInt64(table, "id")
	o.MerchantsID = field.NewInt64(table, "merchants_id")
	o.TransactionID = field.NewString(table, "transaction_id")
	o.OrderID = field.NewString(table, "order_id")
	o.MerchantOrderID = field.NewString(table, "merchant_order_id")
	o.PayAddress = field.NewString(table, "pay_address")
	o.PayScore = field.NewString(table, "pay_score")
	o.Address = field.NewString(table, "address")
	o.AddressID = field.NewInt64(table, "address_id")
	o.Amount = field.NewFloat64(table, "amount")
	o.Status = field.NewInt32(table, "status")
	o.CallbackStatus = field.NewInt32(table, "callback_status")
	o.CallbackURL = field.NewString(table, "callback_url")
	o.CallbackNum = field.NewInt32(table, "callback_num")
	o.CallbackResult = field.NewString(table, "callback_result")
	o.Rate = field.NewFloat64(table, "rate")
	o.RealAmount = field.NewFloat64(table, "real_amount")
	o.ChainName = field.NewString(table, "chain_name")
	o.ReturnURL = field.NewString(table, "return_url")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.DeletedAt = field.NewField(table, "deleted_at")

	o.fillFieldMap()

	return o
}

func (o *orders) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *orders) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 22)
	o.fieldMap["id"] = o.ID
	o.fieldMap["merchants_id"] = o.MerchantsID
	o.fieldMap["transaction_id"] = o.TransactionID
	o.fieldMap["order_id"] = o.OrderID
	o.fieldMap["merchant_order_id"] = o.MerchantOrderID
	o.fieldMap["pay_address"] = o.PayAddress
	o.fieldMap["pay_score"] = o.PayScore
	o.fieldMap["address"] = o.Address
	o.fieldMap["address_id"] = o.AddressID
	o.fieldMap["amount"] = o.Amount
	o.fieldMap["status"] = o.Status
	o.fieldMap["callback_status"] = o.CallbackStatus
	o.fieldMap["callback_url"] = o.CallbackURL
	o.fieldMap["callback_num"] = o.CallbackNum
	o.fieldMap["callback_result"] = o.CallbackResult
	o.fieldMap["rate"] = o.Rate
	o.fieldMap["real_amount"] = o.RealAmount
	o.fieldMap["chain_name"] = o.ChainName
	o.fieldMap["return_url"] = o.ReturnURL
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
}

func (o orders) clone(db *gorm.DB) orders {
	o.ordersDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o orders) replaceDB(db *gorm.DB) orders {
	o.ordersDo.ReplaceDB(db)
	return o
}

type ordersDo struct{ gen.DO }

func (o ordersDo) Debug() *ordersDo {
	return o.withDO(o.DO.Debug())
}

func (o ordersDo) WithContext(ctx context.Context) *ordersDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o ordersDo) ReadDB() *ordersDo {
	return o.Clauses(dbresolver.Read)
}

func (o ordersDo) WriteDB() *ordersDo {
	return o.Clauses(dbresolver.Write)
}

func (o ordersDo) Session(config *gorm.Session) *ordersDo {
	return o.withDO(o.DO.Session(config))
}

func (o ordersDo) Clauses(conds ...clause.Expression) *ordersDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o ordersDo) Returning(value interface{}, columns ...string) *ordersDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o ordersDo) Not(conds ...gen.Condition) *ordersDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o ordersDo) Or(conds ...gen.Condition) *ordersDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o ordersDo) Select(conds ...field.Expr) *ordersDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o ordersDo) Where(conds ...gen.Condition) *ordersDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o ordersDo) Order(conds ...field.Expr) *ordersDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o ordersDo) Distinct(cols ...field.Expr) *ordersDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o ordersDo) Omit(cols ...field.Expr) *ordersDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o ordersDo) Join(table schema.Tabler, on ...field.Expr) *ordersDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o ordersDo) LeftJoin(table schema.Tabler, on ...field.Expr) *ordersDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o ordersDo) RightJoin(table schema.Tabler, on ...field.Expr) *ordersDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o ordersDo) Group(cols ...field.Expr) *ordersDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o ordersDo) Having(conds ...gen.Condition) *ordersDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o ordersDo) Limit(limit int) *ordersDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o ordersDo) Offset(offset int) *ordersDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o ordersDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *ordersDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o ordersDo) Unscoped() *ordersDo {
	return o.withDO(o.DO.Unscoped())
}

func (o ordersDo) Create(values ...*entity.Orders) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o ordersDo) CreateInBatches(values []*entity.Orders, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o ordersDo) Save(values ...*entity.Orders) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o ordersDo) First() (*entity.Orders, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Orders), nil
	}
}

func (o ordersDo) Take() (*entity.Orders, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Orders), nil
	}
}

func (o ordersDo) Last() (*entity.Orders, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Orders), nil
	}
}

func (o ordersDo) Find() ([]*entity.Orders, error) {
	result, err := o.DO.Find()
	return result.([]*entity.Orders), err
}

func (o ordersDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Orders, err error) {
	buf := make([]*entity.Orders, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o ordersDo) FindInBatches(result *[]*entity.Orders, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o ordersDo) Attrs(attrs ...field.AssignExpr) *ordersDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o ordersDo) Assign(attrs ...field.AssignExpr) *ordersDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o ordersDo) Joins(fields ...field.RelationField) *ordersDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o ordersDo) Preload(fields ...field.RelationField) *ordersDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o ordersDo) FirstOrInit() (*entity.Orders, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Orders), nil
	}
}

func (o ordersDo) FirstOrCreate() (*entity.Orders, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Orders), nil
	}
}

func (o ordersDo) FindByPage(offset int, limit int) (result []*entity.Orders, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o ordersDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o ordersDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o ordersDo) Delete(models ...*entity.Orders) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *ordersDo) withDO(do gen.Dao) *ordersDo {
	o.DO = *do.(*gen.DO)
	return o
}
