// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newCollectionRecord(db *gorm.DB, opts ...gen.DOOption) collectionRecord {
	_collectionRecord := collectionRecord{}

	_collectionRecord.collectionRecordDo.UseDB(db, opts...)
	_collectionRecord.collectionRecordDo.UseModel(&entity.CollectionRecord{})

	tableName := _collectionRecord.collectionRecordDo.TableName()
	_collectionRecord.ALL = field.NewAsterisk(tableName)
	_collectionRecord.ID = field.NewInt64(tableName, "id")
	_collectionRecord.Type = field.NewInt32(tableName, "type")
	_collectionRecord.Amount = field.NewFloat64(tableName, "amount")
	_collectionRecord.AfterCollectionAmount = field.NewFloat64(tableName, "after_collection_amount")
	_collectionRecord.AddressChainAssetID = field.NewInt64(tableName, "address_chain_asset_id")
	_collectionRecord.Status = field.NewInt32(tableName, "status")
	_collectionRecord.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_collectionRecord.ChainName = field.NewString(tableName, "chain_name")
	_collectionRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_collectionRecord.UpdatedAt = field.NewTime(tableName, "updated_at")
	_collectionRecord.EndatedAt = field.NewTime(tableName, "endated_at")
	_collectionRecord.DeletedAt = field.NewField(tableName, "deleted_at")
	_collectionRecord.FromAddress = field.NewString(tableName, "from_address")
	_collectionRecord.ToAddress = field.NewString(tableName, "to_address")
	_collectionRecord.Fee = field.NewFloat64(tableName, "fee")
	_collectionRecord.TxHash = field.NewString(tableName, "tx_hash")

	_collectionRecord.fillFieldMap()

	return _collectionRecord
}

type collectionRecord struct {
	collectionRecordDo

	ALL                   field.Asterisk
	ID                    field.Int64
	Type                  field.Int32   // 1商户  2 代理
	Amount                field.Float64 // 归集金额
	AfterCollectionAmount field.Float64 // 归集后地址余额
	AddressChainAssetID   field.Int64   // address_chain_asset 表id 用于关联地址
	Status                field.Int32   // 0 待处理  1转账手续费中 2转账USDT中  3已完成
	MerchantsID           field.Int64   // 商户id
	ChainName             field.String
	CreatedAt             field.Time // 创建时间
	UpdatedAt             field.Time // 更新时间
	EndatedAt             field.Time
	DeletedAt             field.Field
	FromAddress           field.String
	ToAddress             field.String
	Fee                   field.Float64
	TxHash                field.String

	fieldMap map[string]field.Expr
}

func (c collectionRecord) Table(newTableName string) *collectionRecord {
	c.collectionRecordDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c collectionRecord) As(alias string) *collectionRecord {
	c.collectionRecordDo.DO = *(c.collectionRecordDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *collectionRecord) updateTableName(table string) *collectionRecord {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.Type = field.NewInt32(table, "type")
	c.Amount = field.NewFloat64(table, "amount")
	c.AfterCollectionAmount = field.NewFloat64(table, "after_collection_amount")
	c.AddressChainAssetID = field.NewInt64(table, "address_chain_asset_id")
	c.Status = field.NewInt32(table, "status")
	c.MerchantsID = field.NewInt64(table, "merchants_id")
	c.ChainName = field.NewString(table, "chain_name")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")
	c.EndatedAt = field.NewTime(table, "endated_at")
	c.DeletedAt = field.NewField(table, "deleted_at")
	c.FromAddress = field.NewString(table, "from_address")
	c.ToAddress = field.NewString(table, "to_address")
	c.Fee = field.NewFloat64(table, "fee")
	c.TxHash = field.NewString(table, "tx_hash")

	c.fillFieldMap()

	return c
}

func (c *collectionRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *collectionRecord) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 16)
	c.fieldMap["id"] = c.ID
	c.fieldMap["type"] = c.Type
	c.fieldMap["amount"] = c.Amount
	c.fieldMap["after_collection_amount"] = c.AfterCollectionAmount
	c.fieldMap["address_chain_asset_id"] = c.AddressChainAssetID
	c.fieldMap["status"] = c.Status
	c.fieldMap["merchants_id"] = c.MerchantsID
	c.fieldMap["chain_name"] = c.ChainName
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["endated_at"] = c.EndatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["from_address"] = c.FromAddress
	c.fieldMap["to_address"] = c.ToAddress
	c.fieldMap["fee"] = c.Fee
	c.fieldMap["tx_hash"] = c.TxHash
}

func (c collectionRecord) clone(db *gorm.DB) collectionRecord {
	c.collectionRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c collectionRecord) replaceDB(db *gorm.DB) collectionRecord {
	c.collectionRecordDo.ReplaceDB(db)
	return c
}

type collectionRecordDo struct{ gen.DO }

func (c collectionRecordDo) Debug() *collectionRecordDo {
	return c.withDO(c.DO.Debug())
}

func (c collectionRecordDo) WithContext(ctx context.Context) *collectionRecordDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c collectionRecordDo) ReadDB() *collectionRecordDo {
	return c.Clauses(dbresolver.Read)
}

func (c collectionRecordDo) WriteDB() *collectionRecordDo {
	return c.Clauses(dbresolver.Write)
}

func (c collectionRecordDo) Session(config *gorm.Session) *collectionRecordDo {
	return c.withDO(c.DO.Session(config))
}

func (c collectionRecordDo) Clauses(conds ...clause.Expression) *collectionRecordDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c collectionRecordDo) Returning(value interface{}, columns ...string) *collectionRecordDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c collectionRecordDo) Not(conds ...gen.Condition) *collectionRecordDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c collectionRecordDo) Or(conds ...gen.Condition) *collectionRecordDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c collectionRecordDo) Select(conds ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c collectionRecordDo) Where(conds ...gen.Condition) *collectionRecordDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c collectionRecordDo) Order(conds ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c collectionRecordDo) Distinct(cols ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c collectionRecordDo) Omit(cols ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c collectionRecordDo) Join(table schema.Tabler, on ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c collectionRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c collectionRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c collectionRecordDo) Group(cols ...field.Expr) *collectionRecordDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c collectionRecordDo) Having(conds ...gen.Condition) *collectionRecordDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c collectionRecordDo) Limit(limit int) *collectionRecordDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c collectionRecordDo) Offset(offset int) *collectionRecordDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c collectionRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *collectionRecordDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c collectionRecordDo) Unscoped() *collectionRecordDo {
	return c.withDO(c.DO.Unscoped())
}

func (c collectionRecordDo) Create(values ...*entity.CollectionRecord) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c collectionRecordDo) CreateInBatches(values []*entity.CollectionRecord, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c collectionRecordDo) Save(values ...*entity.CollectionRecord) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c collectionRecordDo) First() (*entity.CollectionRecord, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.CollectionRecord), nil
	}
}

func (c collectionRecordDo) Take() (*entity.CollectionRecord, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.CollectionRecord), nil
	}
}

func (c collectionRecordDo) Last() (*entity.CollectionRecord, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.CollectionRecord), nil
	}
}

func (c collectionRecordDo) Find() ([]*entity.CollectionRecord, error) {
	result, err := c.DO.Find()
	return result.([]*entity.CollectionRecord), err
}

func (c collectionRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.CollectionRecord, err error) {
	buf := make([]*entity.CollectionRecord, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c collectionRecordDo) FindInBatches(result *[]*entity.CollectionRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c collectionRecordDo) Attrs(attrs ...field.AssignExpr) *collectionRecordDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c collectionRecordDo) Assign(attrs ...field.AssignExpr) *collectionRecordDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c collectionRecordDo) Joins(fields ...field.RelationField) *collectionRecordDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c collectionRecordDo) Preload(fields ...field.RelationField) *collectionRecordDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c collectionRecordDo) FirstOrInit() (*entity.CollectionRecord, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.CollectionRecord), nil
	}
}

func (c collectionRecordDo) FirstOrCreate() (*entity.CollectionRecord, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.CollectionRecord), nil
	}
}

func (c collectionRecordDo) FindByPage(offset int, limit int) (result []*entity.CollectionRecord, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c collectionRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c collectionRecordDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c collectionRecordDo) Delete(models ...*entity.CollectionRecord) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *collectionRecordDo) withDO(do gen.Dao) *collectionRecordDo {
	c.DO = *do.(*gen.DO)
	return c
}
