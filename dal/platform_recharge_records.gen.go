// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newPlatformRechargeRecords(db *gorm.DB, opts ...gen.DOOption) platformRechargeRecords {
	_platformRechargeRecords := platformRechargeRecords{}

	_platformRechargeRecords.platformRechargeRecordsDo.UseDB(db, opts...)
	_platformRechargeRecords.platformRechargeRecordsDo.UseModel(&entity.PlatformRechargeRecords{})

	tableName := _platformRechargeRecords.platformRechargeRecordsDo.TableName()
	_platformRechargeRecords.ALL = field.NewAsterisk(tableName)
	_platformRechargeRecords.ID = field.NewInt64(tableName, "id")
	_platformRechargeRecords.ChainName = field.NewString(tableName, "chain_name")
	_platformRechargeRecords.Amount = field.NewFloat64(tableName, "amount")
	_platformRechargeRecords.UserID = field.NewInt64(tableName, "user_id")
	_platformRechargeRecords.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_platformRechargeRecords.Remark = field.NewString(tableName, "remark")
	_platformRechargeRecords.CreatedAt = field.NewTime(tableName, "created_at")
	_platformRechargeRecords.UpdatedAt = field.NewTime(tableName, "updated_at")

	_platformRechargeRecords.fillFieldMap()

	return _platformRechargeRecords
}

type platformRechargeRecords struct {
	platformRechargeRecordsDo

	ALL         field.Asterisk
	ID          field.Int64
	ChainName   field.String  // 充值链的名称
	Amount      field.Float64 // 充值金额
	UserID      field.Int64   // 管理员id
	MerchantsID field.Int64   // 商户id
	Remark      field.String
	CreatedAt   field.Time // 创建时间
	UpdatedAt   field.Time // 更新时间

	fieldMap map[string]field.Expr
}

func (p platformRechargeRecords) Table(newTableName string) *platformRechargeRecords {
	p.platformRechargeRecordsDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p platformRechargeRecords) As(alias string) *platformRechargeRecords {
	p.platformRechargeRecordsDo.DO = *(p.platformRechargeRecordsDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *platformRechargeRecords) updateTableName(table string) *platformRechargeRecords {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.ChainName = field.NewString(table, "chain_name")
	p.Amount = field.NewFloat64(table, "amount")
	p.UserID = field.NewInt64(table, "user_id")
	p.MerchantsID = field.NewInt64(table, "merchants_id")
	p.Remark = field.NewString(table, "remark")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")

	p.fillFieldMap()

	return p
}

func (p *platformRechargeRecords) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *platformRechargeRecords) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 8)
	p.fieldMap["id"] = p.ID
	p.fieldMap["chain_name"] = p.ChainName
	p.fieldMap["amount"] = p.Amount
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["merchants_id"] = p.MerchantsID
	p.fieldMap["remark"] = p.Remark
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
}

func (p platformRechargeRecords) clone(db *gorm.DB) platformRechargeRecords {
	p.platformRechargeRecordsDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p platformRechargeRecords) replaceDB(db *gorm.DB) platformRechargeRecords {
	p.platformRechargeRecordsDo.ReplaceDB(db)
	return p
}

type platformRechargeRecordsDo struct{ gen.DO }

func (p platformRechargeRecordsDo) Debug() *platformRechargeRecordsDo {
	return p.withDO(p.DO.Debug())
}

func (p platformRechargeRecordsDo) WithContext(ctx context.Context) *platformRechargeRecordsDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p platformRechargeRecordsDo) ReadDB() *platformRechargeRecordsDo {
	return p.Clauses(dbresolver.Read)
}

func (p platformRechargeRecordsDo) WriteDB() *platformRechargeRecordsDo {
	return p.Clauses(dbresolver.Write)
}

func (p platformRechargeRecordsDo) Session(config *gorm.Session) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Session(config))
}

func (p platformRechargeRecordsDo) Clauses(conds ...clause.Expression) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p platformRechargeRecordsDo) Returning(value interface{}, columns ...string) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p platformRechargeRecordsDo) Not(conds ...gen.Condition) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p platformRechargeRecordsDo) Or(conds ...gen.Condition) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p platformRechargeRecordsDo) Select(conds ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p platformRechargeRecordsDo) Where(conds ...gen.Condition) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p platformRechargeRecordsDo) Order(conds ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p platformRechargeRecordsDo) Distinct(cols ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p platformRechargeRecordsDo) Omit(cols ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p platformRechargeRecordsDo) Join(table schema.Tabler, on ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p platformRechargeRecordsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p platformRechargeRecordsDo) RightJoin(table schema.Tabler, on ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p platformRechargeRecordsDo) Group(cols ...field.Expr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p platformRechargeRecordsDo) Having(conds ...gen.Condition) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p platformRechargeRecordsDo) Limit(limit int) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p platformRechargeRecordsDo) Offset(offset int) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p platformRechargeRecordsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p platformRechargeRecordsDo) Unscoped() *platformRechargeRecordsDo {
	return p.withDO(p.DO.Unscoped())
}

func (p platformRechargeRecordsDo) Create(values ...*entity.PlatformRechargeRecords) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p platformRechargeRecordsDo) CreateInBatches(values []*entity.PlatformRechargeRecords, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p platformRechargeRecordsDo) Save(values ...*entity.PlatformRechargeRecords) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p platformRechargeRecordsDo) First() (*entity.PlatformRechargeRecords, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlatformRechargeRecords), nil
	}
}

func (p platformRechargeRecordsDo) Take() (*entity.PlatformRechargeRecords, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlatformRechargeRecords), nil
	}
}

func (p platformRechargeRecordsDo) Last() (*entity.PlatformRechargeRecords, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlatformRechargeRecords), nil
	}
}

func (p platformRechargeRecordsDo) Find() ([]*entity.PlatformRechargeRecords, error) {
	result, err := p.DO.Find()
	return result.([]*entity.PlatformRechargeRecords), err
}

func (p platformRechargeRecordsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.PlatformRechargeRecords, err error) {
	buf := make([]*entity.PlatformRechargeRecords, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p platformRechargeRecordsDo) FindInBatches(result *[]*entity.PlatformRechargeRecords, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p platformRechargeRecordsDo) Attrs(attrs ...field.AssignExpr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p platformRechargeRecordsDo) Assign(attrs ...field.AssignExpr) *platformRechargeRecordsDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p platformRechargeRecordsDo) Joins(fields ...field.RelationField) *platformRechargeRecordsDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p platformRechargeRecordsDo) Preload(fields ...field.RelationField) *platformRechargeRecordsDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p platformRechargeRecordsDo) FirstOrInit() (*entity.PlatformRechargeRecords, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlatformRechargeRecords), nil
	}
}

func (p platformRechargeRecordsDo) FirstOrCreate() (*entity.PlatformRechargeRecords, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlatformRechargeRecords), nil
	}
}

func (p platformRechargeRecordsDo) FindByPage(offset int, limit int) (result []*entity.PlatformRechargeRecords, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p platformRechargeRecordsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p platformRechargeRecordsDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p platformRechargeRecordsDo) Delete(models ...*entity.PlatformRechargeRecords) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *platformRechargeRecordsDo) withDO(do gen.Dao) *platformRechargeRecordsDo {
	p.DO = *do.(*gen.DO)
	return p
}
