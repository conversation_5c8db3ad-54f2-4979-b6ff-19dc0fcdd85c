// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newAdmins(db *gorm.DB, opts ...gen.DOOption) admins {
	_admins := admins{}

	_admins.adminsDo.UseDB(db, opts...)
	_admins.adminsDo.UseModel(&entity.Admins{})

	tableName := _admins.adminsDo.TableName()
	_admins.ALL = field.NewAsterisk(tableName)
	_admins.ID = field.NewInt64(tableName, "id")
	_admins.Username = field.NewString(tableName, "username")
	_admins.Password = field.NewField(tableName, "password")
	_admins.Nickname = field.NewString(tableName, "nickname")
	_admins.Avatar = field.NewString(tableName, "avatar")
	_admins.Status = field.NewInt32(tableName, "status")
	_admins.CreatedAt = field.NewTime(tableName, "created_at")
	_admins.UpdatedAt = field.NewTime(tableName, "updated_at")
	_admins.DeletedAt = field.NewField(tableName, "deleted_at")

	_admins.fillFieldMap()

	return _admins
}

type admins struct {
	adminsDo

	ALL       field.Asterisk
	ID        field.Int64
	Username  field.String // 用户名
	Password  field.Field  // 密码
	Nickname  field.String // 昵称
	Avatar    field.String // 头像
	Status    field.Int32  // 0 禁用  1正常
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field

	fieldMap map[string]field.Expr
}

func (a admins) Table(newTableName string) *admins {
	a.adminsDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a admins) As(alias string) *admins {
	a.adminsDo.DO = *(a.adminsDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *admins) updateTableName(table string) *admins {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.Username = field.NewString(table, "username")
	a.Password = field.NewField(table, "password")
	a.Nickname = field.NewString(table, "nickname")
	a.Avatar = field.NewString(table, "avatar")
	a.Status = field.NewInt32(table, "status")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *admins) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *admins) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["username"] = a.Username
	a.fieldMap["password"] = a.Password
	a.fieldMap["nickname"] = a.Nickname
	a.fieldMap["avatar"] = a.Avatar
	a.fieldMap["status"] = a.Status
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a admins) clone(db *gorm.DB) admins {
	a.adminsDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a admins) replaceDB(db *gorm.DB) admins {
	a.adminsDo.ReplaceDB(db)
	return a
}

type adminsDo struct{ gen.DO }

func (a adminsDo) Debug() *adminsDo {
	return a.withDO(a.DO.Debug())
}

func (a adminsDo) WithContext(ctx context.Context) *adminsDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a adminsDo) ReadDB() *adminsDo {
	return a.Clauses(dbresolver.Read)
}

func (a adminsDo) WriteDB() *adminsDo {
	return a.Clauses(dbresolver.Write)
}

func (a adminsDo) Session(config *gorm.Session) *adminsDo {
	return a.withDO(a.DO.Session(config))
}

func (a adminsDo) Clauses(conds ...clause.Expression) *adminsDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a adminsDo) Returning(value interface{}, columns ...string) *adminsDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a adminsDo) Not(conds ...gen.Condition) *adminsDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a adminsDo) Or(conds ...gen.Condition) *adminsDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a adminsDo) Select(conds ...field.Expr) *adminsDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a adminsDo) Where(conds ...gen.Condition) *adminsDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a adminsDo) Order(conds ...field.Expr) *adminsDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a adminsDo) Distinct(cols ...field.Expr) *adminsDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a adminsDo) Omit(cols ...field.Expr) *adminsDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a adminsDo) Join(table schema.Tabler, on ...field.Expr) *adminsDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a adminsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *adminsDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a adminsDo) RightJoin(table schema.Tabler, on ...field.Expr) *adminsDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a adminsDo) Group(cols ...field.Expr) *adminsDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a adminsDo) Having(conds ...gen.Condition) *adminsDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a adminsDo) Limit(limit int) *adminsDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a adminsDo) Offset(offset int) *adminsDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a adminsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *adminsDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a adminsDo) Unscoped() *adminsDo {
	return a.withDO(a.DO.Unscoped())
}

func (a adminsDo) Create(values ...*entity.Admins) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a adminsDo) CreateInBatches(values []*entity.Admins, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a adminsDo) Save(values ...*entity.Admins) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a adminsDo) First() (*entity.Admins, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Admins), nil
	}
}

func (a adminsDo) Take() (*entity.Admins, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Admins), nil
	}
}

func (a adminsDo) Last() (*entity.Admins, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Admins), nil
	}
}

func (a adminsDo) Find() ([]*entity.Admins, error) {
	result, err := a.DO.Find()
	return result.([]*entity.Admins), err
}

func (a adminsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Admins, err error) {
	buf := make([]*entity.Admins, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a adminsDo) FindInBatches(result *[]*entity.Admins, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a adminsDo) Attrs(attrs ...field.AssignExpr) *adminsDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a adminsDo) Assign(attrs ...field.AssignExpr) *adminsDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a adminsDo) Joins(fields ...field.RelationField) *adminsDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a adminsDo) Preload(fields ...field.RelationField) *adminsDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a adminsDo) FirstOrInit() (*entity.Admins, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Admins), nil
	}
}

func (a adminsDo) FirstOrCreate() (*entity.Admins, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Admins), nil
	}
}

func (a adminsDo) FindByPage(offset int, limit int) (result []*entity.Admins, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a adminsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a adminsDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a adminsDo) Delete(models ...*entity.Admins) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *adminsDo) withDO(do gen.Dao) *adminsDo {
	a.DO = *do.(*gen.DO)
	return a
}
