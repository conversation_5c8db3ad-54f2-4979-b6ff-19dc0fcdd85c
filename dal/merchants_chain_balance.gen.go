// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newMerchantsChainBalance(db *gorm.DB, opts ...gen.DOOption) merchantsChainBalance {
	_merchantsChainBalance := merchantsChainBalance{}

	_merchantsChainBalance.merchantsChainBalanceDo.UseDB(db, opts...)
	_merchantsChainBalance.merchantsChainBalanceDo.UseModel(&entity.MerchantsChainBalance{})

	tableName := _merchantsChainBalance.merchantsChainBalanceDo.TableName()
	_merchantsChainBalance.ALL = field.NewAsterisk(tableName)
	_merchantsChainBalance.ID = field.NewInt64(tableName, "id")
	_merchantsChainBalance.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_merchantsChainBalance.Balance = field.NewFloat64(tableName, "balance")
	_merchantsChainBalance.LockBalance = field.NewFloat64(tableName, "lock_balance")
	_merchantsChainBalance.FeeBalance = field.NewFloat64(tableName, "fee_balance")
	_merchantsChainBalance.GatherStatus = field.NewInt32(tableName, "gather_status")
	_merchantsChainBalance.NeedGatherBalance = field.NewFloat64(tableName, "need_gather_balance")
	_merchantsChainBalance.LastUpdated = field.NewTime(tableName, "last_updated")
	_merchantsChainBalance.ChainName = field.NewString(tableName, "chain_name")

	_merchantsChainBalance.fillFieldMap()

	return _merchantsChainBalance
}

type merchantsChainBalance struct {
	merchantsChainBalanceDo

	ALL               field.Asterisk
	ID                field.Int64
	MerchantsID       field.Int64   // 商户ID
	Balance           field.Float64 // 主余额
	LockBalance       field.Float64 // 锁定余额
	FeeBalance        field.Float64 // 手续费余额
	GatherStatus      field.Int32   // 归集状态 0未执行 1等待 2执行中
	NeedGatherBalance field.Float64 // 待归集余额
	LastUpdated       field.Time    // 最后更新时间
	ChainName         field.String  // 区块链ID

	fieldMap map[string]field.Expr
}

func (m merchantsChainBalance) Table(newTableName string) *merchantsChainBalance {
	m.merchantsChainBalanceDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchantsChainBalance) As(alias string) *merchantsChainBalance {
	m.merchantsChainBalanceDo.DO = *(m.merchantsChainBalanceDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchantsChainBalance) updateTableName(table string) *merchantsChainBalance {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.MerchantsID = field.NewInt64(table, "merchants_id")
	m.Balance = field.NewFloat64(table, "balance")
	m.LockBalance = field.NewFloat64(table, "lock_balance")
	m.FeeBalance = field.NewFloat64(table, "fee_balance")
	m.GatherStatus = field.NewInt32(table, "gather_status")
	m.NeedGatherBalance = field.NewFloat64(table, "need_gather_balance")
	m.LastUpdated = field.NewTime(table, "last_updated")
	m.ChainName = field.NewString(table, "chain_name")

	m.fillFieldMap()

	return m
}

func (m *merchantsChainBalance) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchantsChainBalance) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 9)
	m.fieldMap["id"] = m.ID
	m.fieldMap["merchants_id"] = m.MerchantsID
	m.fieldMap["balance"] = m.Balance
	m.fieldMap["lock_balance"] = m.LockBalance
	m.fieldMap["fee_balance"] = m.FeeBalance
	m.fieldMap["gather_status"] = m.GatherStatus
	m.fieldMap["need_gather_balance"] = m.NeedGatherBalance
	m.fieldMap["last_updated"] = m.LastUpdated
	m.fieldMap["chain_name"] = m.ChainName
}

func (m merchantsChainBalance) clone(db *gorm.DB) merchantsChainBalance {
	m.merchantsChainBalanceDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchantsChainBalance) replaceDB(db *gorm.DB) merchantsChainBalance {
	m.merchantsChainBalanceDo.ReplaceDB(db)
	return m
}

type merchantsChainBalanceDo struct{ gen.DO }

func (m merchantsChainBalanceDo) Debug() *merchantsChainBalanceDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantsChainBalanceDo) WithContext(ctx context.Context) *merchantsChainBalanceDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantsChainBalanceDo) ReadDB() *merchantsChainBalanceDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantsChainBalanceDo) WriteDB() *merchantsChainBalanceDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantsChainBalanceDo) Session(config *gorm.Session) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantsChainBalanceDo) Clauses(conds ...clause.Expression) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantsChainBalanceDo) Returning(value interface{}, columns ...string) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantsChainBalanceDo) Not(conds ...gen.Condition) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantsChainBalanceDo) Or(conds ...gen.Condition) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantsChainBalanceDo) Select(conds ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantsChainBalanceDo) Where(conds ...gen.Condition) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantsChainBalanceDo) Order(conds ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantsChainBalanceDo) Distinct(cols ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantsChainBalanceDo) Omit(cols ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantsChainBalanceDo) Join(table schema.Tabler, on ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantsChainBalanceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantsChainBalanceDo) RightJoin(table schema.Tabler, on ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantsChainBalanceDo) Group(cols ...field.Expr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantsChainBalanceDo) Having(conds ...gen.Condition) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantsChainBalanceDo) Limit(limit int) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantsChainBalanceDo) Offset(offset int) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantsChainBalanceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantsChainBalanceDo) Unscoped() *merchantsChainBalanceDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantsChainBalanceDo) Create(values ...*entity.MerchantsChainBalance) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantsChainBalanceDo) CreateInBatches(values []*entity.MerchantsChainBalance, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantsChainBalanceDo) Save(values ...*entity.MerchantsChainBalance) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantsChainBalanceDo) First() (*entity.MerchantsChainBalance, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainBalance), nil
	}
}

func (m merchantsChainBalanceDo) Take() (*entity.MerchantsChainBalance, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainBalance), nil
	}
}

func (m merchantsChainBalanceDo) Last() (*entity.MerchantsChainBalance, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainBalance), nil
	}
}

func (m merchantsChainBalanceDo) Find() ([]*entity.MerchantsChainBalance, error) {
	result, err := m.DO.Find()
	return result.([]*entity.MerchantsChainBalance), err
}

func (m merchantsChainBalanceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.MerchantsChainBalance, err error) {
	buf := make([]*entity.MerchantsChainBalance, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantsChainBalanceDo) FindInBatches(result *[]*entity.MerchantsChainBalance, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantsChainBalanceDo) Attrs(attrs ...field.AssignExpr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantsChainBalanceDo) Assign(attrs ...field.AssignExpr) *merchantsChainBalanceDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantsChainBalanceDo) Joins(fields ...field.RelationField) *merchantsChainBalanceDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantsChainBalanceDo) Preload(fields ...field.RelationField) *merchantsChainBalanceDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantsChainBalanceDo) FirstOrInit() (*entity.MerchantsChainBalance, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainBalance), nil
	}
}

func (m merchantsChainBalanceDo) FirstOrCreate() (*entity.MerchantsChainBalance, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.MerchantsChainBalance), nil
	}
}

func (m merchantsChainBalanceDo) FindByPage(offset int, limit int) (result []*entity.MerchantsChainBalance, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantsChainBalanceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantsChainBalanceDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantsChainBalanceDo) Delete(models ...*entity.MerchantsChainBalance) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantsChainBalanceDo) withDO(do gen.Dao) *merchantsChainBalanceDo {
	m.DO = *do.(*gen.DO)
	return m
}
