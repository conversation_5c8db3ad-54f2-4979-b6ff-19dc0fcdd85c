// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newMessages(db *gorm.DB, opts ...gen.DOOption) messages {
	_messages := messages{}

	_messages.messagesDo.UseDB(db, opts...)
	_messages.messagesDo.UseModel(&entity.Messages{})

	tableName := _messages.messagesDo.TableName()
	_messages.ALL = field.NewAsterisk(tableName)
	_messages.ID = field.NewInt64(tableName, "id")
	_messages.Content = field.NewString(tableName, "content")
	_messages.Status = field.NewBool(tableName, "status")
	_messages.CreatedAt = field.NewTime(tableName, "created_at")
	_messages.UpdatedAt = field.NewTime(tableName, "updated_at")
	_messages.DeletedAt = field.NewField(tableName, "deleted_at")

	_messages.fillFieldMap()

	return _messages
}

type messages struct {
	messagesDo

	ALL       field.Asterisk
	ID        field.Int64
	Content   field.String // 内容
	Status    field.Bool   // 是否已读
	CreatedAt field.Time   // 创建时间
	UpdatedAt field.Time   // 更新时间
	DeletedAt field.Field

	fieldMap map[string]field.Expr
}

func (m messages) Table(newTableName string) *messages {
	m.messagesDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m messages) As(alias string) *messages {
	m.messagesDo.DO = *(m.messagesDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *messages) updateTableName(table string) *messages {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.Content = field.NewString(table, "content")
	m.Status = field.NewBool(table, "status")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")

	m.fillFieldMap()

	return m
}

func (m *messages) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *messages) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["id"] = m.ID
	m.fieldMap["content"] = m.Content
	m.fieldMap["status"] = m.Status
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
}

func (m messages) clone(db *gorm.DB) messages {
	m.messagesDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m messages) replaceDB(db *gorm.DB) messages {
	m.messagesDo.ReplaceDB(db)
	return m
}

type messagesDo struct{ gen.DO }

func (m messagesDo) Debug() *messagesDo {
	return m.withDO(m.DO.Debug())
}

func (m messagesDo) WithContext(ctx context.Context) *messagesDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m messagesDo) ReadDB() *messagesDo {
	return m.Clauses(dbresolver.Read)
}

func (m messagesDo) WriteDB() *messagesDo {
	return m.Clauses(dbresolver.Write)
}

func (m messagesDo) Session(config *gorm.Session) *messagesDo {
	return m.withDO(m.DO.Session(config))
}

func (m messagesDo) Clauses(conds ...clause.Expression) *messagesDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m messagesDo) Returning(value interface{}, columns ...string) *messagesDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m messagesDo) Not(conds ...gen.Condition) *messagesDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m messagesDo) Or(conds ...gen.Condition) *messagesDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m messagesDo) Select(conds ...field.Expr) *messagesDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m messagesDo) Where(conds ...gen.Condition) *messagesDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m messagesDo) Order(conds ...field.Expr) *messagesDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m messagesDo) Distinct(cols ...field.Expr) *messagesDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m messagesDo) Omit(cols ...field.Expr) *messagesDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m messagesDo) Join(table schema.Tabler, on ...field.Expr) *messagesDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m messagesDo) LeftJoin(table schema.Tabler, on ...field.Expr) *messagesDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m messagesDo) RightJoin(table schema.Tabler, on ...field.Expr) *messagesDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m messagesDo) Group(cols ...field.Expr) *messagesDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m messagesDo) Having(conds ...gen.Condition) *messagesDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m messagesDo) Limit(limit int) *messagesDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m messagesDo) Offset(offset int) *messagesDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m messagesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *messagesDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m messagesDo) Unscoped() *messagesDo {
	return m.withDO(m.DO.Unscoped())
}

func (m messagesDo) Create(values ...*entity.Messages) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m messagesDo) CreateInBatches(values []*entity.Messages, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m messagesDo) Save(values ...*entity.Messages) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m messagesDo) First() (*entity.Messages, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Messages), nil
	}
}

func (m messagesDo) Take() (*entity.Messages, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Messages), nil
	}
}

func (m messagesDo) Last() (*entity.Messages, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Messages), nil
	}
}

func (m messagesDo) Find() ([]*entity.Messages, error) {
	result, err := m.DO.Find()
	return result.([]*entity.Messages), err
}

func (m messagesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Messages, err error) {
	buf := make([]*entity.Messages, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m messagesDo) FindInBatches(result *[]*entity.Messages, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m messagesDo) Attrs(attrs ...field.AssignExpr) *messagesDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m messagesDo) Assign(attrs ...field.AssignExpr) *messagesDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m messagesDo) Joins(fields ...field.RelationField) *messagesDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m messagesDo) Preload(fields ...field.RelationField) *messagesDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m messagesDo) FirstOrInit() (*entity.Messages, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Messages), nil
	}
}

func (m messagesDo) FirstOrCreate() (*entity.Messages, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Messages), nil
	}
}

func (m messagesDo) FindByPage(offset int, limit int) (result []*entity.Messages, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m messagesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m messagesDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m messagesDo) Delete(models ...*entity.Messages) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *messagesDo) withDO(do gen.Dao) *messagesDo {
	m.DO = *do.(*gen.DO)
	return m
}
