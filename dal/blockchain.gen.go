// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newBlockchain(db *gorm.DB, opts ...gen.DOOption) blockchain {
	_blockchain := blockchain{}

	_blockchain.blockchainDo.UseDB(db, opts...)
	_blockchain.blockchainDo.UseModel(&entity.Blockchain{})

	tableName := _blockchain.blockchainDo.TableName()
	_blockchain.ALL = field.NewAsterisk(tableName)
	_blockchain.ChainName = field.NewString(tableName, "chain_name")
	_blockchain.NativeCurrency = field.NewString(tableName, "native_currency")
	_blockchain.IsActive = field.NewBool(tableName, "is_active")
	_blockchain.CreatedAt = field.NewTime(tableName, "created_at")
	_blockchain.UpdatedAt = field.NewTime(tableName, "updated_at")
	_blockchain.Network = field.NewString(tableName, "network")
	_blockchain.Fee = field.NewFloat64(tableName, "fee")
	_blockchain.MinCollectAmount = field.NewFloat64(tableName, "min_collect_amount")
	_blockchain.KeepAmount = field.NewFloat64(tableName, "keep_amount")
	_blockchain.BatchSize = field.NewInt32(tableName, "batch_size")
	_blockchain.MaxRetry = field.NewInt32(tableName, "max_retry")
	_blockchain.IntervalMinutes = field.NewInt32(tableName, "interval_minutes")

	_blockchain.fillFieldMap()

	return _blockchain
}

type blockchain struct {
	blockchainDo

	ALL              field.Asterisk
	ChainName        field.String // 区块链名称(如:TRC,ERC,BSC)
	NativeCurrency   field.String // 原生代币(如:TRX,ETH,BNB)
	IsActive         field.Bool   // 是否启用该链
	CreatedAt        field.Time   // 创建时间
	UpdatedAt        field.Time   // 更新时间
	Network          field.String // 网络类型
	Fee              field.Float64
	MinCollectAmount field.Float64 // 最小归集金额
	KeepAmount       field.Float64 // 预留手续费金额
	BatchSize        field.Int32   // 批处理大小
	MaxRetry         field.Int32   // 最大重试次数
	IntervalMinutes  field.Int32   // 归集间隔（分钟）

	fieldMap map[string]field.Expr
}

func (b blockchain) Table(newTableName string) *blockchain {
	b.blockchainDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b blockchain) As(alias string) *blockchain {
	b.blockchainDo.DO = *(b.blockchainDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *blockchain) updateTableName(table string) *blockchain {
	b.ALL = field.NewAsterisk(table)
	b.ChainName = field.NewString(table, "chain_name")
	b.NativeCurrency = field.NewString(table, "native_currency")
	b.IsActive = field.NewBool(table, "is_active")
	b.CreatedAt = field.NewTime(table, "created_at")
	b.UpdatedAt = field.NewTime(table, "updated_at")
	b.Network = field.NewString(table, "network")
	b.Fee = field.NewFloat64(table, "fee")
	b.MinCollectAmount = field.NewFloat64(table, "min_collect_amount")
	b.KeepAmount = field.NewFloat64(table, "keep_amount")
	b.BatchSize = field.NewInt32(table, "batch_size")
	b.MaxRetry = field.NewInt32(table, "max_retry")
	b.IntervalMinutes = field.NewInt32(table, "interval_minutes")

	b.fillFieldMap()

	return b
}

func (b *blockchain) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *blockchain) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 12)
	b.fieldMap["chain_name"] = b.ChainName
	b.fieldMap["native_currency"] = b.NativeCurrency
	b.fieldMap["is_active"] = b.IsActive
	b.fieldMap["created_at"] = b.CreatedAt
	b.fieldMap["updated_at"] = b.UpdatedAt
	b.fieldMap["network"] = b.Network
	b.fieldMap["fee"] = b.Fee
	b.fieldMap["min_collect_amount"] = b.MinCollectAmount
	b.fieldMap["keep_amount"] = b.KeepAmount
	b.fieldMap["batch_size"] = b.BatchSize
	b.fieldMap["max_retry"] = b.MaxRetry
	b.fieldMap["interval_minutes"] = b.IntervalMinutes
}

func (b blockchain) clone(db *gorm.DB) blockchain {
	b.blockchainDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b blockchain) replaceDB(db *gorm.DB) blockchain {
	b.blockchainDo.ReplaceDB(db)
	return b
}

type blockchainDo struct{ gen.DO }

func (b blockchainDo) Debug() *blockchainDo {
	return b.withDO(b.DO.Debug())
}

func (b blockchainDo) WithContext(ctx context.Context) *blockchainDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b blockchainDo) ReadDB() *blockchainDo {
	return b.Clauses(dbresolver.Read)
}

func (b blockchainDo) WriteDB() *blockchainDo {
	return b.Clauses(dbresolver.Write)
}

func (b blockchainDo) Session(config *gorm.Session) *blockchainDo {
	return b.withDO(b.DO.Session(config))
}

func (b blockchainDo) Clauses(conds ...clause.Expression) *blockchainDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b blockchainDo) Returning(value interface{}, columns ...string) *blockchainDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b blockchainDo) Not(conds ...gen.Condition) *blockchainDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b blockchainDo) Or(conds ...gen.Condition) *blockchainDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b blockchainDo) Select(conds ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b blockchainDo) Where(conds ...gen.Condition) *blockchainDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b blockchainDo) Order(conds ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b blockchainDo) Distinct(cols ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b blockchainDo) Omit(cols ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b blockchainDo) Join(table schema.Tabler, on ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b blockchainDo) LeftJoin(table schema.Tabler, on ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b blockchainDo) RightJoin(table schema.Tabler, on ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b blockchainDo) Group(cols ...field.Expr) *blockchainDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b blockchainDo) Having(conds ...gen.Condition) *blockchainDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b blockchainDo) Limit(limit int) *blockchainDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b blockchainDo) Offset(offset int) *blockchainDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b blockchainDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *blockchainDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b blockchainDo) Unscoped() *blockchainDo {
	return b.withDO(b.DO.Unscoped())
}

func (b blockchainDo) Create(values ...*entity.Blockchain) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b blockchainDo) CreateInBatches(values []*entity.Blockchain, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b blockchainDo) Save(values ...*entity.Blockchain) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b blockchainDo) First() (*entity.Blockchain, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Blockchain), nil
	}
}

func (b blockchainDo) Take() (*entity.Blockchain, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Blockchain), nil
	}
}

func (b blockchainDo) Last() (*entity.Blockchain, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Blockchain), nil
	}
}

func (b blockchainDo) Find() ([]*entity.Blockchain, error) {
	result, err := b.DO.Find()
	return result.([]*entity.Blockchain), err
}

func (b blockchainDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Blockchain, err error) {
	buf := make([]*entity.Blockchain, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b blockchainDo) FindInBatches(result *[]*entity.Blockchain, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b blockchainDo) Attrs(attrs ...field.AssignExpr) *blockchainDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b blockchainDo) Assign(attrs ...field.AssignExpr) *blockchainDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b blockchainDo) Joins(fields ...field.RelationField) *blockchainDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b blockchainDo) Preload(fields ...field.RelationField) *blockchainDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b blockchainDo) FirstOrInit() (*entity.Blockchain, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Blockchain), nil
	}
}

func (b blockchainDo) FirstOrCreate() (*entity.Blockchain, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Blockchain), nil
	}
}

func (b blockchainDo) FindByPage(offset int, limit int) (result []*entity.Blockchain, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b blockchainDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b blockchainDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b blockchainDo) Delete(models ...*entity.Blockchain) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *blockchainDo) withDO(do gen.Dao) *blockchainDo {
	b.DO = *do.(*gen.DO)
	return b
}
