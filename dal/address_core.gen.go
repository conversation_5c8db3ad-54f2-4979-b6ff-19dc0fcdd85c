// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newAddressCore(db *gorm.DB, opts ...gen.DOOption) addressCore {
	_addressCore := addressCore{}

	_addressCore.addressCoreDo.UseDB(db, opts...)
	_addressCore.addressCoreDo.UseModel(&entity.AddressCore{})

	tableName := _addressCore.addressCoreDo.TableName()
	_addressCore.ALL = field.NewAsterisk(tableName)
	_addressCore.ID = field.NewInt64(tableName, "id")
	_addressCore.PrivateKey = field.NewString(tableName, "private_key")
	_addressCore.UserID = field.NewString(tableName, "user_id")
	_addressCore.CallbackURL = field.NewString(tableName, "callback_url")
	_addressCore.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_addressCore.Lock = field.NewBool(tableName, "lock")
	_addressCore.Locktime = field.NewInt64(tableName, "locktime")
	_addressCore.Status = field.NewInt32(tableName, "status")
	_addressCore.CreatedAt = field.NewTime(tableName, "created_at")
	_addressCore.UpdatedAt = field.NewTime(tableName, "updated_at")
	_addressCore.DeletedAt = field.NewField(tableName, "deleted_at")

	_addressCore.fillFieldMap()

	return _addressCore
}

type addressCore struct {
	addressCoreDo

	ALL         field.Asterisk
	ID          field.Int64
	PrivateKey  field.String // 私钥
	UserID      field.String // 商户绑定用户ID
	CallbackURL field.String // 回调地址
	MerchantsID field.Int64  // 商户ID
	Lock        field.Bool   // 全局锁定状态
	Locktime    field.Int64  // 全局锁定时间
	Status      field.Int32  // 0 启用 1禁用
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field

	fieldMap map[string]field.Expr
}

func (a addressCore) Table(newTableName string) *addressCore {
	a.addressCoreDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a addressCore) As(alias string) *addressCore {
	a.addressCoreDo.DO = *(a.addressCoreDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *addressCore) updateTableName(table string) *addressCore {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.PrivateKey = field.NewString(table, "private_key")
	a.UserID = field.NewString(table, "user_id")
	a.CallbackURL = field.NewString(table, "callback_url")
	a.MerchantsID = field.NewInt64(table, "merchants_id")
	a.Lock = field.NewBool(table, "lock")
	a.Locktime = field.NewInt64(table, "locktime")
	a.Status = field.NewInt32(table, "status")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *addressCore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *addressCore) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 11)
	a.fieldMap["id"] = a.ID
	a.fieldMap["private_key"] = a.PrivateKey
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["callback_url"] = a.CallbackURL
	a.fieldMap["merchants_id"] = a.MerchantsID
	a.fieldMap["lock"] = a.Lock
	a.fieldMap["locktime"] = a.Locktime
	a.fieldMap["status"] = a.Status
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a addressCore) clone(db *gorm.DB) addressCore {
	a.addressCoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a addressCore) replaceDB(db *gorm.DB) addressCore {
	a.addressCoreDo.ReplaceDB(db)
	return a
}

type addressCoreDo struct{ gen.DO }

func (a addressCoreDo) Debug() *addressCoreDo {
	return a.withDO(a.DO.Debug())
}

func (a addressCoreDo) WithContext(ctx context.Context) *addressCoreDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a addressCoreDo) ReadDB() *addressCoreDo {
	return a.Clauses(dbresolver.Read)
}

func (a addressCoreDo) WriteDB() *addressCoreDo {
	return a.Clauses(dbresolver.Write)
}

func (a addressCoreDo) Session(config *gorm.Session) *addressCoreDo {
	return a.withDO(a.DO.Session(config))
}

func (a addressCoreDo) Clauses(conds ...clause.Expression) *addressCoreDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a addressCoreDo) Returning(value interface{}, columns ...string) *addressCoreDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a addressCoreDo) Not(conds ...gen.Condition) *addressCoreDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a addressCoreDo) Or(conds ...gen.Condition) *addressCoreDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a addressCoreDo) Select(conds ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a addressCoreDo) Where(conds ...gen.Condition) *addressCoreDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a addressCoreDo) Order(conds ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a addressCoreDo) Distinct(cols ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a addressCoreDo) Omit(cols ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a addressCoreDo) Join(table schema.Tabler, on ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a addressCoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a addressCoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a addressCoreDo) Group(cols ...field.Expr) *addressCoreDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a addressCoreDo) Having(conds ...gen.Condition) *addressCoreDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a addressCoreDo) Limit(limit int) *addressCoreDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a addressCoreDo) Offset(offset int) *addressCoreDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a addressCoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *addressCoreDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a addressCoreDo) Unscoped() *addressCoreDo {
	return a.withDO(a.DO.Unscoped())
}

func (a addressCoreDo) Create(values ...*entity.AddressCore) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a addressCoreDo) CreateInBatches(values []*entity.AddressCore, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a addressCoreDo) Save(values ...*entity.AddressCore) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a addressCoreDo) First() (*entity.AddressCore, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCore), nil
	}
}

func (a addressCoreDo) Take() (*entity.AddressCore, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCore), nil
	}
}

func (a addressCoreDo) Last() (*entity.AddressCore, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCore), nil
	}
}

func (a addressCoreDo) Find() ([]*entity.AddressCore, error) {
	result, err := a.DO.Find()
	return result.([]*entity.AddressCore), err
}

func (a addressCoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.AddressCore, err error) {
	buf := make([]*entity.AddressCore, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a addressCoreDo) FindInBatches(result *[]*entity.AddressCore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a addressCoreDo) Attrs(attrs ...field.AssignExpr) *addressCoreDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a addressCoreDo) Assign(attrs ...field.AssignExpr) *addressCoreDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a addressCoreDo) Joins(fields ...field.RelationField) *addressCoreDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a addressCoreDo) Preload(fields ...field.RelationField) *addressCoreDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a addressCoreDo) FirstOrInit() (*entity.AddressCore, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCore), nil
	}
}

func (a addressCoreDo) FirstOrCreate() (*entity.AddressCore, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCore), nil
	}
}

func (a addressCoreDo) FindByPage(offset int, limit int) (result []*entity.AddressCore, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a addressCoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a addressCoreDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a addressCoreDo) Delete(models ...*entity.AddressCore) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *addressCoreDo) withDO(do gen.Dao) *addressCoreDo {
	a.DO = *do.(*gen.DO)
	return a
}
