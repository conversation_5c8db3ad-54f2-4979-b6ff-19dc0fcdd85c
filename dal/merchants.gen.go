// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newMerchants(db *gorm.DB, opts ...gen.DOOption) merchants {
	_merchants := merchants{}

	_merchants.merchantsDo.UseDB(db, opts...)
	_merchants.merchantsDo.UseModel(&entity.Merchants{})

	tableName := _merchants.merchantsDo.TableName()
	_merchants.ALL = field.NewAsterisk(tableName)
	_merchants.ID = field.NewInt64(tableName, "id")
	_merchants.CreatedAt = field.NewTime(tableName, "created_at")
	_merchants.UpdatedAt = field.NewTime(tableName, "updated_at")
	_merchants.DeletedAt = field.NewField(tableName, "deleted_at")
	_merchants.Username = field.NewString(tableName, "username")
	_merchants.Password = field.NewField(tableName, "password")
	_merchants.Status = field.NewInt32(tableName, "status")
	_merchants.Secret = field.NewString(tableName, "secret")
	_merchants.Paysecret = field.NewString(tableName, "paysecret")
	_merchants.AgentID = field.NewInt32(tableName, "agent_id")
	_merchants.AgentName = field.NewString(tableName, "agent_name")
	_merchants.Appkey = field.NewString(tableName, "appkey")
	_merchants.Token = field.NewString(tableName, "token")
	_merchants.Credits = field.NewFloat64(tableName, "credits")
	_merchants.LimitAmount = field.NewFloat64(tableName, "limit_amount")
	_merchants.CommissionPrice = field.NewFloat64(tableName, "commission_price")
	_merchants.Rate = field.NewFloat64(tableName, "rate")
	_merchants.Floatratetype = field.NewFloat64(tableName, "floatratetype")
	_merchants.Floatratedvalue = field.NewFloat64(tableName, "floatratedvalue")
	_merchants.IsFloatdown = field.NewBool(tableName, "is_floatdown")
	_merchants.Ordertimeout = field.NewFloat64(tableName, "ordertimeout")
	_merchants.Orderlocktime = field.NewFloat64(tableName, "orderlocktime")
	_merchants.Orderamount = field.NewFloat64(tableName, "orderamount")
	_merchants.GatherType = field.NewInt16(tableName, "gather_type")

	_merchants.fillFieldMap()

	return _merchants
}

type merchants struct {
	merchantsDo

	ALL             field.Asterisk
	ID              field.Int64
	CreatedAt       field.Time    // 创建时间
	UpdatedAt       field.Time    // 更新时间
	DeletedAt       field.Field   // 删除时间
	Username        field.String  // 用户名
	Password        field.Field   // 密码
	Status          field.Int32   // 状态 启用0 禁用1
	Secret          field.String  // Google密钥
	Paysecret       field.String  // 下发Google密钥
	AgentID         field.Int32   // 所属代理ID
	AgentName       field.String  // 所属代理名
	Appkey          field.String  // API密钥
	Token           field.String  // 认证令牌
	Credits         field.Float64 // 信用额度(可透支手续费)
	LimitAmount     field.Float64 // 限制最低订单金额
	CommissionPrice field.Float64 // 固定按笔手续费
	Rate            field.Float64 // 汇率值
	Floatratetype   field.Float64 // 浮动类型 1百分比 2固定值
	Floatratedvalue field.Float64 // 浮动值
	IsFloatdown     field.Bool    // 是否开启下浮
	Ordertimeout    field.Float64 // 订单超时时间(秒)
	Orderlocktime   field.Float64 // 订单锁定时间(秒)
	Orderamount     field.Float64 // 大额订单阈值
	GatherType      field.Int16   // 归集类型 1自动 2下发时

	fieldMap map[string]field.Expr
}

func (m merchants) Table(newTableName string) *merchants {
	m.merchantsDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchants) As(alias string) *merchants {
	m.merchantsDo.DO = *(m.merchantsDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchants) updateTableName(table string) *merchants {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")
	m.Username = field.NewString(table, "username")
	m.Password = field.NewField(table, "password")
	m.Status = field.NewInt32(table, "status")
	m.Secret = field.NewString(table, "secret")
	m.Paysecret = field.NewString(table, "paysecret")
	m.AgentID = field.NewInt32(table, "agent_id")
	m.AgentName = field.NewString(table, "agent_name")
	m.Appkey = field.NewString(table, "appkey")
	m.Token = field.NewString(table, "token")
	m.Credits = field.NewFloat64(table, "credits")
	m.LimitAmount = field.NewFloat64(table, "limit_amount")
	m.CommissionPrice = field.NewFloat64(table, "commission_price")
	m.Rate = field.NewFloat64(table, "rate")
	m.Floatratetype = field.NewFloat64(table, "floatratetype")
	m.Floatratedvalue = field.NewFloat64(table, "floatratedvalue")
	m.IsFloatdown = field.NewBool(table, "is_floatdown")
	m.Ordertimeout = field.NewFloat64(table, "ordertimeout")
	m.Orderlocktime = field.NewFloat64(table, "orderlocktime")
	m.Orderamount = field.NewFloat64(table, "orderamount")
	m.GatherType = field.NewInt16(table, "gather_type")

	m.fillFieldMap()

	return m
}

func (m *merchants) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchants) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 24)
	m.fieldMap["id"] = m.ID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
	m.fieldMap["username"] = m.Username
	m.fieldMap["password"] = m.Password
	m.fieldMap["status"] = m.Status
	m.fieldMap["secret"] = m.Secret
	m.fieldMap["paysecret"] = m.Paysecret
	m.fieldMap["agent_id"] = m.AgentID
	m.fieldMap["agent_name"] = m.AgentName
	m.fieldMap["appkey"] = m.Appkey
	m.fieldMap["token"] = m.Token
	m.fieldMap["credits"] = m.Credits
	m.fieldMap["limit_amount"] = m.LimitAmount
	m.fieldMap["commission_price"] = m.CommissionPrice
	m.fieldMap["rate"] = m.Rate
	m.fieldMap["floatratetype"] = m.Floatratetype
	m.fieldMap["floatratedvalue"] = m.Floatratedvalue
	m.fieldMap["is_floatdown"] = m.IsFloatdown
	m.fieldMap["ordertimeout"] = m.Ordertimeout
	m.fieldMap["orderlocktime"] = m.Orderlocktime
	m.fieldMap["orderamount"] = m.Orderamount
	m.fieldMap["gather_type"] = m.GatherType
}

func (m merchants) clone(db *gorm.DB) merchants {
	m.merchantsDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchants) replaceDB(db *gorm.DB) merchants {
	m.merchantsDo.ReplaceDB(db)
	return m
}

type merchantsDo struct{ gen.DO }

func (m merchantsDo) Debug() *merchantsDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantsDo) WithContext(ctx context.Context) *merchantsDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantsDo) ReadDB() *merchantsDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantsDo) WriteDB() *merchantsDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantsDo) Session(config *gorm.Session) *merchantsDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantsDo) Clauses(conds ...clause.Expression) *merchantsDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantsDo) Returning(value interface{}, columns ...string) *merchantsDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantsDo) Not(conds ...gen.Condition) *merchantsDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantsDo) Or(conds ...gen.Condition) *merchantsDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantsDo) Select(conds ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantsDo) Where(conds ...gen.Condition) *merchantsDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantsDo) Order(conds ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantsDo) Distinct(cols ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantsDo) Omit(cols ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantsDo) Join(table schema.Tabler, on ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantsDo) RightJoin(table schema.Tabler, on ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantsDo) Group(cols ...field.Expr) *merchantsDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantsDo) Having(conds ...gen.Condition) *merchantsDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantsDo) Limit(limit int) *merchantsDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantsDo) Offset(offset int) *merchantsDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *merchantsDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantsDo) Unscoped() *merchantsDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantsDo) Create(values ...*entity.Merchants) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantsDo) CreateInBatches(values []*entity.Merchants, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantsDo) Save(values ...*entity.Merchants) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantsDo) First() (*entity.Merchants, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchants), nil
	}
}

func (m merchantsDo) Take() (*entity.Merchants, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchants), nil
	}
}

func (m merchantsDo) Last() (*entity.Merchants, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchants), nil
	}
}

func (m merchantsDo) Find() ([]*entity.Merchants, error) {
	result, err := m.DO.Find()
	return result.([]*entity.Merchants), err
}

func (m merchantsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Merchants, err error) {
	buf := make([]*entity.Merchants, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantsDo) FindInBatches(result *[]*entity.Merchants, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantsDo) Attrs(attrs ...field.AssignExpr) *merchantsDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantsDo) Assign(attrs ...field.AssignExpr) *merchantsDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantsDo) Joins(fields ...field.RelationField) *merchantsDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantsDo) Preload(fields ...field.RelationField) *merchantsDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantsDo) FirstOrInit() (*entity.Merchants, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchants), nil
	}
}

func (m merchantsDo) FirstOrCreate() (*entity.Merchants, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchants), nil
	}
}

func (m merchantsDo) FindByPage(offset int, limit int) (result []*entity.Merchants, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantsDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantsDo) Delete(models ...*entity.Merchants) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantsDo) withDO(do gen.Dao) *merchantsDo {
	m.DO = *do.(*gen.DO)
	return m
}
