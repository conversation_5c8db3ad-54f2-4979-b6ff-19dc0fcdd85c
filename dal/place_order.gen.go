// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"payAPI/model/entity"
)

func newPlaceOrder(db *gorm.DB, opts ...gen.DOOption) placeOrder {
	_placeOrder := placeOrder{}

	_placeOrder.placeOrderDo.UseDB(db, opts...)
	_placeOrder.placeOrderDo.UseModel(&entity.PlaceOrder{})

	tableName := _placeOrder.placeOrderDo.TableName()
	_placeOrder.ALL = field.NewAsterisk(tableName)
	_placeOrder.ID = field.NewInt64(tableName, "id")
	_placeOrder.MerchantsID = field.NewInt64(tableName, "merchants_id")
	_placeOrder.TransactionID = field.NewString(tableName, "transaction_id")
	_placeOrder.OrderID = field.NewString(tableName, "order_id")
	_placeOrder.MerchantOrderID = field.NewString(tableName, "merchant_order_id")
	_placeOrder.Address = field.NewString(tableName, "address")
	_placeOrder.Amount = field.NewFloat64(tableName, "amount")
	_placeOrder.Coin = field.NewString(tableName, "coin")
	_placeOrder.EstimatedMinerFee = field.NewFloat64(tableName, "estimated_miner_fee")
	_placeOrder.ActualMinerFee = field.NewFloat64(tableName, "actual_miner_fee")
	_placeOrder.Status = field.NewInt32(tableName, "status")
	_placeOrder.CallbackStatus = field.NewInt32(tableName, "callback_status")
	_placeOrder.CallbackURL = field.NewString(tableName, "callback_url")
	_placeOrder.CallbackNum = field.NewInt32(tableName, "callback_num")
	_placeOrder.CallbackResult = field.NewString(tableName, "callback_result")
	_placeOrder.ReturnURL = field.NewString(tableName, "return_url")
	_placeOrder.Rate = field.NewFloat64(tableName, "rate")
	_placeOrder.ChainName = field.NewString(tableName, "chain_name")
	_placeOrder.EnergyStatus = field.NewInt32(tableName, "energy_status")
	_placeOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_placeOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_placeOrder.DeletedAt = field.NewField(tableName, "deleted_at")

	_placeOrder.fillFieldMap()

	return _placeOrder
}

type placeOrder struct {
	placeOrderDo

	ALL               field.Asterisk
	ID                field.Int64
	MerchantsID       field.Int64   // 商户id
	TransactionID     field.String  // 交易编号
	OrderID           field.String  // 订单号
	MerchantOrderID   field.String  // 商户订单号
	Address           field.String  // 收款地址
	Amount            field.Float64 // 金额
	Coin              field.String  // 币种
	EstimatedMinerFee field.Float64 // 预估矿工费
	ActualMinerFee    field.Float64 // 实际花费矿工费
	Status            field.Int32   // 0:未转账 1:转账成功  2:主动成功 3:超时 4:失败
	CallbackStatus    field.Int32   // 0未回调  1回调成功 2回调失败
	CallbackURL       field.String  // 回调地址
	CallbackNum       field.Int32   // 回调次数
	CallbackResult    field.String  // 回调返回信息
	ReturnURL         field.String  // 跳转地址
	Rate              field.Float64 // 汇率
	ChainName         field.String  // 链
	EnergyStatus      field.Int32   // 能量是否到账 0 未到账 1 已到账
	CreatedAt         field.Time
	UpdatedAt         field.Time
	DeletedAt         field.Field

	fieldMap map[string]field.Expr
}

func (p placeOrder) Table(newTableName string) *placeOrder {
	p.placeOrderDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p placeOrder) As(alias string) *placeOrder {
	p.placeOrderDo.DO = *(p.placeOrderDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *placeOrder) updateTableName(table string) *placeOrder {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.MerchantsID = field.NewInt64(table, "merchants_id")
	p.TransactionID = field.NewString(table, "transaction_id")
	p.OrderID = field.NewString(table, "order_id")
	p.MerchantOrderID = field.NewString(table, "merchant_order_id")
	p.Address = field.NewString(table, "address")
	p.Amount = field.NewFloat64(table, "amount")
	p.Coin = field.NewString(table, "coin")
	p.EstimatedMinerFee = field.NewFloat64(table, "estimated_miner_fee")
	p.ActualMinerFee = field.NewFloat64(table, "actual_miner_fee")
	p.Status = field.NewInt32(table, "status")
	p.CallbackStatus = field.NewInt32(table, "callback_status")
	p.CallbackURL = field.NewString(table, "callback_url")
	p.CallbackNum = field.NewInt32(table, "callback_num")
	p.CallbackResult = field.NewString(table, "callback_result")
	p.ReturnURL = field.NewString(table, "return_url")
	p.Rate = field.NewFloat64(table, "rate")
	p.ChainName = field.NewString(table, "chain_name")
	p.EnergyStatus = field.NewInt32(table, "energy_status")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")

	p.fillFieldMap()

	return p
}

func (p *placeOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *placeOrder) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 22)
	p.fieldMap["id"] = p.ID
	p.fieldMap["merchants_id"] = p.MerchantsID
	p.fieldMap["transaction_id"] = p.TransactionID
	p.fieldMap["order_id"] = p.OrderID
	p.fieldMap["merchant_order_id"] = p.MerchantOrderID
	p.fieldMap["address"] = p.Address
	p.fieldMap["amount"] = p.Amount
	p.fieldMap["coin"] = p.Coin
	p.fieldMap["estimated_miner_fee"] = p.EstimatedMinerFee
	p.fieldMap["actual_miner_fee"] = p.ActualMinerFee
	p.fieldMap["status"] = p.Status
	p.fieldMap["callback_status"] = p.CallbackStatus
	p.fieldMap["callback_url"] = p.CallbackURL
	p.fieldMap["callback_num"] = p.CallbackNum
	p.fieldMap["callback_result"] = p.CallbackResult
	p.fieldMap["return_url"] = p.ReturnURL
	p.fieldMap["rate"] = p.Rate
	p.fieldMap["chain_name"] = p.ChainName
	p.fieldMap["energy_status"] = p.EnergyStatus
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
}

func (p placeOrder) clone(db *gorm.DB) placeOrder {
	p.placeOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p placeOrder) replaceDB(db *gorm.DB) placeOrder {
	p.placeOrderDo.ReplaceDB(db)
	return p
}

type placeOrderDo struct{ gen.DO }

func (p placeOrderDo) Debug() *placeOrderDo {
	return p.withDO(p.DO.Debug())
}

func (p placeOrderDo) WithContext(ctx context.Context) *placeOrderDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p placeOrderDo) ReadDB() *placeOrderDo {
	return p.Clauses(dbresolver.Read)
}

func (p placeOrderDo) WriteDB() *placeOrderDo {
	return p.Clauses(dbresolver.Write)
}

func (p placeOrderDo) Session(config *gorm.Session) *placeOrderDo {
	return p.withDO(p.DO.Session(config))
}

func (p placeOrderDo) Clauses(conds ...clause.Expression) *placeOrderDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p placeOrderDo) Returning(value interface{}, columns ...string) *placeOrderDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p placeOrderDo) Not(conds ...gen.Condition) *placeOrderDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p placeOrderDo) Or(conds ...gen.Condition) *placeOrderDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p placeOrderDo) Select(conds ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p placeOrderDo) Where(conds ...gen.Condition) *placeOrderDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p placeOrderDo) Order(conds ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p placeOrderDo) Distinct(cols ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p placeOrderDo) Omit(cols ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p placeOrderDo) Join(table schema.Tabler, on ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p placeOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p placeOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p placeOrderDo) Group(cols ...field.Expr) *placeOrderDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p placeOrderDo) Having(conds ...gen.Condition) *placeOrderDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p placeOrderDo) Limit(limit int) *placeOrderDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p placeOrderDo) Offset(offset int) *placeOrderDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p placeOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *placeOrderDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p placeOrderDo) Unscoped() *placeOrderDo {
	return p.withDO(p.DO.Unscoped())
}

func (p placeOrderDo) Create(values ...*entity.PlaceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p placeOrderDo) CreateInBatches(values []*entity.PlaceOrder, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p placeOrderDo) Save(values ...*entity.PlaceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p placeOrderDo) First() (*entity.PlaceOrder, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlaceOrder), nil
	}
}

func (p placeOrderDo) Take() (*entity.PlaceOrder, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlaceOrder), nil
	}
}

func (p placeOrderDo) Last() (*entity.PlaceOrder, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlaceOrder), nil
	}
}

func (p placeOrderDo) Find() ([]*entity.PlaceOrder, error) {
	result, err := p.DO.Find()
	return result.([]*entity.PlaceOrder), err
}

func (p placeOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.PlaceOrder, err error) {
	buf := make([]*entity.PlaceOrder, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p placeOrderDo) FindInBatches(result *[]*entity.PlaceOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p placeOrderDo) Attrs(attrs ...field.AssignExpr) *placeOrderDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p placeOrderDo) Assign(attrs ...field.AssignExpr) *placeOrderDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p placeOrderDo) Joins(fields ...field.RelationField) *placeOrderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p placeOrderDo) Preload(fields ...field.RelationField) *placeOrderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p placeOrderDo) FirstOrInit() (*entity.PlaceOrder, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlaceOrder), nil
	}
}

func (p placeOrderDo) FirstOrCreate() (*entity.PlaceOrder, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.PlaceOrder), nil
	}
}

func (p placeOrderDo) FindByPage(offset int, limit int) (result []*entity.PlaceOrder, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p placeOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p placeOrderDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p placeOrderDo) Delete(models ...*entity.PlaceOrder) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *placeOrderDo) withDO(do gen.Dao) *placeOrderDo {
	p.DO = *do.(*gen.DO)
	return p
}
