// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                         = new(Query)
	AddressChainAsset         *addressChainAsset
	AddressCore               *addressCore
	Admins                    *admins
	Blockchain                *blockchain
	CollectionRecord          *collectionRecord
	LockAddress               *lockAddress
	Merchants                 *merchants
	MerchantsChainBalance     *merchantsChainBalance
	MerchantsChainConfig      *merchantsChainConfig
	Messages                  *messages
	OrderRecords              *orderRecords
	Orders                    *orders
	PlaceOrder                *placeOrder
	PlatformRechargeRecords   *platformRechargeRecords
	RentalEnergyAddressConfig *rentalEnergyAddressConfig
	WhiteList                 *whiteList
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	AddressChainAsset = &Q.AddressChainAsset
	AddressCore = &Q.AddressCore
	Admins = &Q.Admins
	Blockchain = &Q.Blockchain
	CollectionRecord = &Q.CollectionRecord
	LockAddress = &Q.LockAddress
	Merchants = &Q.Merchants
	MerchantsChainBalance = &Q.MerchantsChainBalance
	MerchantsChainConfig = &Q.MerchantsChainConfig
	Messages = &Q.Messages
	OrderRecords = &Q.OrderRecords
	Orders = &Q.Orders
	PlaceOrder = &Q.PlaceOrder
	PlatformRechargeRecords = &Q.PlatformRechargeRecords
	RentalEnergyAddressConfig = &Q.RentalEnergyAddressConfig
	WhiteList = &Q.WhiteList
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                        db,
		AddressChainAsset:         newAddressChainAsset(db, opts...),
		AddressCore:               newAddressCore(db, opts...),
		Admins:                    newAdmins(db, opts...),
		Blockchain:                newBlockchain(db, opts...),
		CollectionRecord:          newCollectionRecord(db, opts...),
		LockAddress:               newLockAddress(db, opts...),
		Merchants:                 newMerchants(db, opts...),
		MerchantsChainBalance:     newMerchantsChainBalance(db, opts...),
		MerchantsChainConfig:      newMerchantsChainConfig(db, opts...),
		Messages:                  newMessages(db, opts...),
		OrderRecords:              newOrderRecords(db, opts...),
		Orders:                    newOrders(db, opts...),
		PlaceOrder:                newPlaceOrder(db, opts...),
		PlatformRechargeRecords:   newPlatformRechargeRecords(db, opts...),
		RentalEnergyAddressConfig: newRentalEnergyAddressConfig(db, opts...),
		WhiteList:                 newWhiteList(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AddressChainAsset         addressChainAsset
	AddressCore               addressCore
	Admins                    admins
	Blockchain                blockchain
	CollectionRecord          collectionRecord
	LockAddress               lockAddress
	Merchants                 merchants
	MerchantsChainBalance     merchantsChainBalance
	MerchantsChainConfig      merchantsChainConfig
	Messages                  messages
	OrderRecords              orderRecords
	Orders                    orders
	PlaceOrder                placeOrder
	PlatformRechargeRecords   platformRechargeRecords
	RentalEnergyAddressConfig rentalEnergyAddressConfig
	WhiteList                 whiteList
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		AddressChainAsset:         q.AddressChainAsset.clone(db),
		AddressCore:               q.AddressCore.clone(db),
		Admins:                    q.Admins.clone(db),
		Blockchain:                q.Blockchain.clone(db),
		CollectionRecord:          q.CollectionRecord.clone(db),
		LockAddress:               q.LockAddress.clone(db),
		Merchants:                 q.Merchants.clone(db),
		MerchantsChainBalance:     q.MerchantsChainBalance.clone(db),
		MerchantsChainConfig:      q.MerchantsChainConfig.clone(db),
		Messages:                  q.Messages.clone(db),
		OrderRecords:              q.OrderRecords.clone(db),
		Orders:                    q.Orders.clone(db),
		PlaceOrder:                q.PlaceOrder.clone(db),
		PlatformRechargeRecords:   q.PlatformRechargeRecords.clone(db),
		RentalEnergyAddressConfig: q.RentalEnergyAddressConfig.clone(db),
		WhiteList:                 q.WhiteList.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		AddressChainAsset:         q.AddressChainAsset.replaceDB(db),
		AddressCore:               q.AddressCore.replaceDB(db),
		Admins:                    q.Admins.replaceDB(db),
		Blockchain:                q.Blockchain.replaceDB(db),
		CollectionRecord:          q.CollectionRecord.replaceDB(db),
		LockAddress:               q.LockAddress.replaceDB(db),
		Merchants:                 q.Merchants.replaceDB(db),
		MerchantsChainBalance:     q.MerchantsChainBalance.replaceDB(db),
		MerchantsChainConfig:      q.MerchantsChainConfig.replaceDB(db),
		Messages:                  q.Messages.replaceDB(db),
		OrderRecords:              q.OrderRecords.replaceDB(db),
		Orders:                    q.Orders.replaceDB(db),
		PlaceOrder:                q.PlaceOrder.replaceDB(db),
		PlatformRechargeRecords:   q.PlatformRechargeRecords.replaceDB(db),
		RentalEnergyAddressConfig: q.RentalEnergyAddressConfig.replaceDB(db),
		WhiteList:                 q.WhiteList.replaceDB(db),
	}
}

type queryCtx struct {
	AddressChainAsset         *addressChainAssetDo
	AddressCore               *addressCoreDo
	Admins                    *adminsDo
	Blockchain                *blockchainDo
	CollectionRecord          *collectionRecordDo
	LockAddress               *lockAddressDo
	Merchants                 *merchantsDo
	MerchantsChainBalance     *merchantsChainBalanceDo
	MerchantsChainConfig      *merchantsChainConfigDo
	Messages                  *messagesDo
	OrderRecords              *orderRecordsDo
	Orders                    *ordersDo
	PlaceOrder                *placeOrderDo
	PlatformRechargeRecords   *platformRechargeRecordsDo
	RentalEnergyAddressConfig *rentalEnergyAddressConfigDo
	WhiteList                 *whiteListDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AddressChainAsset:         q.AddressChainAsset.WithContext(ctx),
		AddressCore:               q.AddressCore.WithContext(ctx),
		Admins:                    q.Admins.WithContext(ctx),
		Blockchain:                q.Blockchain.WithContext(ctx),
		CollectionRecord:          q.CollectionRecord.WithContext(ctx),
		LockAddress:               q.LockAddress.WithContext(ctx),
		Merchants:                 q.Merchants.WithContext(ctx),
		MerchantsChainBalance:     q.MerchantsChainBalance.WithContext(ctx),
		MerchantsChainConfig:      q.MerchantsChainConfig.WithContext(ctx),
		Messages:                  q.Messages.WithContext(ctx),
		OrderRecords:              q.OrderRecords.WithContext(ctx),
		Orders:                    q.Orders.WithContext(ctx),
		PlaceOrder:                q.PlaceOrder.WithContext(ctx),
		PlatformRechargeRecords:   q.PlatformRechargeRecords.WithContext(ctx),
		RentalEnergyAddressConfig: q.RentalEnergyAddressConfig.WithContext(ctx),
		WhiteList:                 q.WhiteList.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
