server:
  port: "8081"
  mode: "development"
  timeout: 30

PostgresSQLConf:
  Enable: true
  User: payUSDTGeneralBackend
  Password: 5JdwwjNDDZmf4HaT
  Host: **************
  Port: 5432
  Database: payUSDTGeneralBackend
  CharSet: utf8
  ParseTime: true
  TimeZOne: Local
  AutoMigrate: true
  Gorm:
    TablePrefix: ""
    SingularTable: true
    MaxOpenConns: 100
    MaxIdleConns: 5
    ConnMaxLifetime: 600

Redisx:
  Host: **************:6379
  Type: node
  Pass: redis_tPxj6f

Log:
  Mode: file
  Path: ./logs
  Level: info
  Rotation: daily # 使用大小切割
  KeepDays: 7
  Compress: false
  FileTimeFormat: "2006-01-02"
  TimeFormat: "2006-01-02 15:04:05"
  ConsoleLogging: true  # 是否同时输出到控制台

Auth:
  AccessSecret: "sss12345678" # AccessSecret的值要是8位以上的字符串
  AccessExpire: 10000 #AccessExpire是过期时间，单位是秒


Cashier:
  url: https://127.0.0.1
  # cashierurl: https://gateway.paygram.pro/cashier

ChainEye:
  ApiUrl: http://**************:8080/api/v1/addChain
  NotifyUrl: http://**************:8085/api/v1/callBackUrl

Crypto:
  # 主私钥，用于生成地址。请在生产环境中使用真实的私钥
  # 可以使用以下命令生成私钥：openssl ecparam -genkey -name secp256k1 -noout -outform DER | xxd -p -c 256
  master_private_key: "d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7"