// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNamePlaceOrder = "place_order"

// PlaceOrder mapped from table <place_order>
type PlaceOrder struct {
	ID                int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	MerchantsID       int64          `gorm:"column:merchants_id;type:bigint;not null;comment:商户id" json:"merchants_id"`                            // 商户id
	TransactionID     *string        `gorm:"column:transaction_id;type:character varying(255);comment:交易编号" json:"transaction_id"`                 // 交易编号
	OrderID           string         `gorm:"column:order_id;type:character varying(255);not null;comment:订单号" json:"order_id"`                     // 订单号
	MerchantOrderID   string         `gorm:"column:merchant_order_id;type:character varying(255);not null;comment:商户订单号" json:"merchant_order_id"` // 商户订单号
	Address           string         `gorm:"column:address;type:character varying(255);not null;comment:收款地址" json:"address"`                      // 收款地址
	Amount            float64        `gorm:"column:amount;type:numeric(10,5);not null;comment:金额" json:"amount"`                                   // 金额
	Coin              string         `gorm:"column:coin;type:character varying(255);not null;comment:币种" json:"coin"`                              // 币种
	EstimatedMinerFee *float64       `gorm:"column:estimated_miner_fee;type:numeric(10,5);comment:预估矿工费" json:"estimated_miner_fee"`               // 预估矿工费
	ActualMinerFee    *float64       `gorm:"column:actual_miner_fee;type:numeric(10,5);comment:实际花费矿工费" json:"actual_miner_fee"`                   // 实际花费矿工费
	Status            *int32         `gorm:"column:status;type:integer;comment:0:未转账 1:转账成功  2:主动成功 3:超时 4:失败" json:"status"`                      // 0:未转账 1:转账成功  2:主动成功 3:超时 4:失败
	CallbackStatus    int32          `gorm:"column:callback_status;type:integer;not null;comment:0未回调  1回调成功 2回调失败" json:"callback_status"`        // 0未回调  1回调成功 2回调失败
	CallbackURL       *string        `gorm:"column:callback_url;type:character varying(255);comment:回调地址" json:"callback_url"`                     // 回调地址
	CallbackNum       *int32         `gorm:"column:callback_num;type:integer;comment:回调次数" json:"callback_num"`                                    // 回调次数
	CallbackResult    *string        `gorm:"column:callback_result;type:json;comment:回调返回信息" json:"callback_result"`                               // 回调返回信息
	ReturnURL         *string        `gorm:"column:return_url;type:character varying(255);comment:跳转地址" json:"return_url"`                         // 跳转地址
	Rate              *float64       `gorm:"column:rate;type:numeric(10,5);default:7;comment:汇率" json:"rate"`                                      // 汇率
	ChainName         *string        `gorm:"column:chain_name;type:character varying(50);comment:链" json:"chain_name"`                             // 链
	EnergyStatus      *int32         `gorm:"column:energy_status;type:integer;comment:能量是否到账 0 未到账 1 已到账" json:"energy_status"`                    // 能量是否到账 0 未到账 1 已到账
	CreatedAt         time.Time      `gorm:"column:created_at;type:timestamp(0) without time zone;not null" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at;type:timestamp(0) without time zone;not null" json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName PlaceOrder's table name
func (*PlaceOrder) TableName() string {
	return TableNamePlaceOrder
}
