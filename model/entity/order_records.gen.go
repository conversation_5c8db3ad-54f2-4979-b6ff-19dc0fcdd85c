// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameOrderRecords = "order_records"

// OrderRecords mapped from table <order_records>
type OrderRecords struct {
	ID            string         `gorm:"column:id;type:character varying(100);primaryKey" json:"id"`
	MerchantsID   *int64         `gorm:"column:merchants_id;type:bigint;comment:商户id" json:"merchants_id"` // 商户id
	ChainName     string         `gorm:"column:chain_name;type:character varying(50);not null" json:"chain_name"`
	Type          int32          `gorm:"column:type;type:integer;not null;default:1;comment:1 商户 2代理" json:"type"`                                                // 1 商户 2代理
	Amount        float64        `gorm:"column:amount;type:numeric(10,5);not null;comment:金额" json:"amount"`                                                      // 金额
	RorderID      *string        `gorm:"column:rorder_id;type:character varying(100);comment:商户订单id" json:"rorder_id"`                                            // 商户订单id
	TransactionID *string        `gorm:"column:transaction_id;type:character varying(100);comment:交易id  hash" json:"transaction_id"`                              // 交易id  hash
	Remark        *string        `gorm:"column:remark;type:character varying(255);comment:备注" json:"remark"`                                                      // 备注
	Status        *int32         `gorm:"column:status;type:integer;default:1;comment:0 禁用  1正常" json:"status"`                                                    // 0 禁用  1正常
	CreatedAt     time.Time      `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt     time.Time      `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName OrderRecords's table name
func (*OrderRecords) TableName() string {
	return TableNameOrderRecords
}
