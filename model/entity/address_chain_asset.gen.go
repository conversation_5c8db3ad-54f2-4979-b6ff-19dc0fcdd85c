// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameAddressChainAsset = "address_chain_asset"

// AddressChainAsset mapped from table <address_chain_asset>
type AddressChainAsset struct {
	ID                 int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	AddressCoreID      int64          `gorm:"column:address_core_id;type:bigint;not null;comment:核心地址ID" json:"address_core_id"`                 // 核心地址ID
	ChainName          string         `gorm:"column:chain_name;type:character varying(50);not null;comment:链类型名称" json:"chain_name"`             // 链类型名称
	Address            string         `gorm:"column:address;type:character varying(255);not null;comment:该链的地址" json:"address"`                  // 该链的地址
	NativeBalance      float64        `gorm:"column:native_balance;type:numeric(10,5);not null;comment:主币余额(TRX/ETH/BNB)" json:"native_balance"` // 主币余额(TRX/ETH/BNB)
	UsdtBalance        float64        `gorm:"column:usdt_balance;type:numeric(10,5);not null;comment:USDT余额" json:"usdt_balance"`                // USDT余额
	TransactionsNums   int32          `gorm:"column:transactions_nums;type:integer;not null;comment:交易笔数" json:"transactions_nums"`              // 交易笔数
	LockStatus         bool           `gorm:"column:lock_status;type:boolean;not null;comment:true 锁定  false 未锁定" json:"lock_status"`            // true 锁定  false 未锁定
	Status             int32          `gorm:"column:status;type:integer;not null;comment:0正常  1不正常" json:"status"`                               // 0正常  1不正常
	CollectHash        *string        `gorm:"column:collect_hash;type:character varying(255);comment:归集交易哈希" json:"collect_hash"`                // 归集交易哈希
	Sid                *int64         `gorm:"column:sid;type:bigint;comment:归集任务序号" json:"sid"`                                                  // 归集任务序号
	MerchantsID        *int64         `gorm:"column:merchants_id;type:bigint;comment:商户ID" json:"merchants_id"`                                  // 商户ID
	CreatedAt          time.Time      `gorm:"column:created_at;type:timestamp(0) without time zone;not null" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"column:updated_at;type:timestamp(0) without time zone;not null" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
	EnergyBalance      int64          `gorm:"column:energy_balance;type:bigint;not null" json:"energy_balance"`
	EnergyUpdatedAt    *time.Time     `gorm:"column:energy_updated_at;type:timestamp without time zone" json:"energy_updated_at"`
	BandwidthBalance   int64          `gorm:"column:bandwidth_balance;type:bigint;not null" json:"bandwidth_balance"`
	BandwidthUpdatedAt *time.Time     `gorm:"column:bandwidth_updated_at;type:timestamp without time zone" json:"bandwidth_updated_at"`
}

// TableName AddressChainAsset's table name
func (*AddressChainAsset) TableName() string {
	return TableNameAddressChainAsset
}
