// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameOrders = "orders"

// Orders mapped from table <orders>
type Orders struct {
	ID              int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	MerchantsID     int64          `gorm:"column:merchants_id;type:bigint;not null" json:"merchants_id"`
	TransactionID   *string        `gorm:"column:transaction_id;type:character varying(255)" json:"transaction_id"`
	OrderID         string         `gorm:"column:order_id;type:character varying(255);not null;comment:订单号" json:"order_id"`                  // 订单号
	MerchantOrderID *string        `gorm:"column:merchant_order_id;type:character varying(255);comment:商户订单号" json:"merchant_order_id"`       // 商户订单号
	PayAddress      string         `gorm:"column:pay_address;type:character varying(255);not null;comment:付款地址" json:"pay_address"`           // 付款地址
	PayScore        string         `gorm:"column:pay_score;type:character varying(255);not null;default:0;comment:付款地址风险评分" json:"pay_score"` // 付款地址风险评分
	Address         string         `gorm:"column:address;type:character varying(255);not null;comment:地址" json:"address"`                     // 地址
	AddressID       int64          `gorm:"column:address_id;type:bigint;not null;comment:地址" json:"address_id"`                               // 地址
	Amount          float64        `gorm:"column:amount;type:numeric(10,5);not null;comment:金额" json:"amount"`                                // 金额
	Status          int32          `gorm:"column:status;type:integer;not null;comment:0:未支付 1:支付成功  2:主动成功 3:订单超时" json:"status"`             // 0:未支付 1:支付成功  2:主动成功 3:订单超时
	CallbackStatus  *int32         `gorm:"column:callback_status;type:integer;comment:0 未回调  1 回调成功  1回调失败" json:"callback_status"`           // 0 未回调  1 回调成功  1回调失败
	CallbackURL     string         `gorm:"column:callback_url;type:character varying(255);not null;comment:回调地址" json:"callback_url"`         // 回调地址
	CallbackNum     int32          `gorm:"column:callback_num;type:integer;not null;comment:回调次数" json:"callback_num"`                        // 回调次数
	CallbackResult  *string        `gorm:"column:callback_result;type:json;comment:回调返回信息" json:"callback_result"`                            // 回调返回信息
	Rate            float64        `gorm:"column:rate;type:numeric(10,2);not null;default:7;comment:汇率" json:"rate"`                          // 汇率
	RealAmount      float64        `gorm:"column:real_amount;type:numeric(10,5);not null;comment:实收金额" json:"real_amount"`                    // 实收金额
	ChainName       string         `gorm:"column:chain_name;type:character varying(50);not null;comment:链id" json:"chain_name"`               // 链id
	ReturnURL       *string        `gorm:"column:return_url;type:character varying(255);comment:跳转地址" json:"return_url"`                      // 跳转地址
	CreatedAt       time.Time      `gorm:"column:created_at;type:timestamp(0) without time zone;not null" json:"created_at"`
	UpdatedAt       time.Time      `gorm:"column:updated_at;type:timestamp(0) without time zone;not null" json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName Orders's table name
func (*Orders) TableName() string {
	return TableNameOrders
}
