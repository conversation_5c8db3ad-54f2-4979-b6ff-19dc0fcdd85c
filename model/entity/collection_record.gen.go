// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameCollectionRecord = "collection_record"

// CollectionRecord mapped from table <collection_record>
type CollectionRecord struct {
	ID                    int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	Type                  int32          `gorm:"column:type;type:integer;not null;default:1;comment:1商户  2 代理" json:"type"`                                      // 1商户  2 代理
	Amount                float64        `gorm:"column:amount;type:numeric(10,5);not null;comment:归集金额" json:"amount"`                                           // 归集金额
	AfterCollectionAmount float64        `gorm:"column:after_collection_amount;type:numeric(10,5);not null;comment:归集后地址余额" json:"after_collection_amount"`      // 归集后地址余额
	AddressChainAssetID   *int64         `gorm:"column:address_chain_asset_id;type:bigint;comment:address_chain_asset 表id 用于关联地址" json:"address_chain_asset_id"` // address_chain_asset 表id 用于关联地址
	Status                int32          `gorm:"column:status;type:integer;not null;comment:0 待处理  1转账手续费中 2转账USDT中  3已完成" json:"status"`                        // 0 待处理  1转账手续费中 2转账USDT中  3已完成
	MerchantsID           *int64         `gorm:"column:merchants_id;type:bigint;comment:商户id" json:"merchants_id"`                                               // 商户id
	ChainName             string         `gorm:"column:chain_name;type:character varying(50);not null" json:"chain_name"`
	CreatedAt             time.Time      `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt             time.Time      `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	EndatedAt             *time.Time     `gorm:"column:endated_at;type:timestamp(6) without time zone" json:"endated_at"`
	DeletedAt             gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
	FromAddress           *string        `gorm:"column:from_address;type:character varying(255)" json:"from_address"`
	ToAddress             *string        `gorm:"column:to_address;type:character varying(255)" json:"to_address"`
	Fee                   *float64       `gorm:"column:fee;type:numeric(10,5)" json:"fee"`
	TxHash                *string        `gorm:"column:tx_hash;type:character varying(255)" json:"tx_hash"`
}

// TableName CollectionRecord's table name
func (*CollectionRecord) TableName() string {
	return TableNameCollectionRecord
}
