// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameWhiteList = "white_list"

// WhiteList mapped from table <white_list>
type WhiteList struct {
	ID          int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	IP          string         `gorm:"column:ip;type:cidr;not null;comment:ip地址" json:"ip"`                          // ip地址
	MerchantsID int64          `gorm:"column:merchants_id;type:bigint;not null;comment:商户id" json:"merchants_id"`    // 商户id
	Type        int32          `gorm:"column:type;type:integer;not null;comment:1 商户后台 2商户api" json:"type"`          // 1 商户后台 2商户api
	Status      int32          `gorm:"column:status;type:integer;not null;default:1;comment:0 禁用 1正常" json:"status"` // 0 禁用 1正常
	CreatedAt   time.Time      `gorm:"column:created_at;type:timestamp(0) without time zone;not null" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;type:timestamp(0) without time zone;not null" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName WhiteList's table name
func (*WhiteList) TableName() string {
	return TableNameWhiteList
}
