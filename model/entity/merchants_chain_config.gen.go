// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameMerchantsChainConfig = "merchants_chain_config"

// MerchantsChainConfig mapped from table <merchants_chain_config>
type MerchantsChainConfig struct {
	ID          int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	MerchantsID int64     `gorm:"column:merchants_id;type:bigint;not null;index:idx_mcc_merchants_chain,priority:2;comment:商户ID" json:"merchants_id"`                                            // 商户ID
	Fee         float64   `gorm:"column:fee;type:numeric(11,2);not null;default:0.00;comment:链费率" json:"fee"`                                                                                    // 链费率
	AgentFee    float64   `gorm:"column:agent_fee;type:numeric(11,2);not null;default:0.00;comment:代理费率" json:"agent_fee"`                                                                       // 代理费率
	IsActive    bool      `gorm:"column:is_active;type:boolean;not null;default:true;comment:是否启用该链配置" json:"is_active"`                                                                         // 是否启用该链配置
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                       // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                       // 更新时间
	Ratetype    float64   `gorm:"column:ratetype;type:numeric(11,0);not null;default:2;comment:汇率类型 1固定 2浮动" json:"ratetype"`                                                                    // 汇率类型 1固定 2浮动
	ChainName   string    `gorm:"column:chain_name;type:character varying(50);not null;index:idx_mcc_chain,priority:1;index:idx_mcc_merchants_chain,priority:1;comment:区块链ID" json:"chain_name"` // 区块链ID
}

// TableName MerchantsChainConfig's table name
func (*MerchantsChainConfig) TableName() string {
	return TableNameMerchantsChainConfig
}
