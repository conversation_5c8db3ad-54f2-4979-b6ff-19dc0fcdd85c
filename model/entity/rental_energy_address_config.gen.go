// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameRentalEnergyAddressConfig = "rental_energy_address_config"

// RentalEnergyAddressConfig mapped from table <rental_energy_address_config>
type RentalEnergyAddressConfig struct {
	ID        int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	Address   string    `gorm:"column:address;type:character varying(255);not null" json:"address"`
	Amount    float64   `gorm:"column:amount;type:numeric(10,5);not null" json:"amount"`
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName RentalEnergyAddressConfig's table name
func (*RentalEnergyAddressConfig) TableName() string {
	return TableNameRentalEnergyAddressConfig
}
