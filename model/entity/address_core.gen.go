// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameAddressCore = "address_core"

// AddressCore mapped from table <address_core>
type AddressCore struct {
	ID          int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	PrivateKey  string         `gorm:"column:private_key;type:character varying(255);not null;comment:私钥" json:"private_key"` // 私钥
	UserID      *string        `gorm:"column:user_id;type:character varying(255);comment:商户绑定用户ID" json:"user_id"`            // 商户绑定用户ID
	CallbackURL *string        `gorm:"column:callback_url;type:character varying(255);comment:回调地址" json:"callback_url"`      // 回调地址
	MerchantsID *int64         `gorm:"column:merchants_id;type:bigint;comment:商户ID" json:"merchants_id"`                      // 商户ID
	Lock        *bool          `gorm:"column:lock;type:boolean;comment:全局锁定状态" json:"lock"`                                   // 全局锁定状态
	Locktime    *int64         `gorm:"column:locktime;type:bigint;comment:全局锁定时间" json:"locktime"`                            // 全局锁定时间
	Status      *int32         `gorm:"column:status;type:integer;comment:0 启用 1禁用" json:"status"`                             // 0 启用 1禁用
	CreatedAt   time.Time      `gorm:"column:created_at;type:timestamp(0) without time zone;not null" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;type:timestamp(0) without time zone;not null" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName AddressCore's table name
func (*AddressCore) TableName() string {
	return TableNameAddressCore
}
