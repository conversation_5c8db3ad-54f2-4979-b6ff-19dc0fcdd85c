// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameLockAddress = "lock_address"

// LockAddress mapped from table <lock_address>
type LockAddress struct {
	ID         int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	AddressID  int64     `gorm:"column:address_id;type:bigint;not null" json:"address_id"`
	Amount     float64   `gorm:"column:amount;type:numeric;not null" json:"amount"`
	LockTime   time.Time `gorm:"column:lock_time;type:timestamp without time zone;not null" json:"lock_time"`
	UnlockTime time.Time `gorm:"column:unlock_time;type:timestamp without time zone;not null" json:"unlock_time"`
	Islock     bool      `gorm:"column:islock;type:boolean;not null" json:"islock"`
}

// TableName LockAddress's table name
func (*LockAddress) TableName() string {
	return TableNameLockAddress
}
