// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePlatformRechargeRecords = "platform_recharge_records"

// PlatformRechargeRecords mapped from table <platform_recharge_records>
type PlatformRechargeRecords struct {
	ID          int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ChainName   string    `gorm:"column:chain_name;type:character varying(100);not null;comment:充值链的名称" json:"chain_name"` // 充值链的名称
	Amount      float64   `gorm:"column:amount;type:numeric(10,5);not null;comment:充值金额" json:"amount"`                    // 充值金额
	UserID      int64     `gorm:"column:user_id;type:bigint;not null;comment:管理员id" json:"user_id"`                        // 管理员id
	MerchantsID int64     `gorm:"column:merchants_id;type:bigint;not null;comment:商户id" json:"merchants_id"`               // 商户id
	Remark      *string   `gorm:"column:remark;type:character varying(255)" json:"remark"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName PlatformRechargeRecords's table name
func (*PlatformRechargeRecords) TableName() string {
	return TableNamePlatformRechargeRecords
}
