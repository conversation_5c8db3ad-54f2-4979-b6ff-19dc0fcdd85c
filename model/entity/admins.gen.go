// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameAdmins = "admins"

// Admins mapped from table <admins>
type Admins struct {
	ID        int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	Username  string         `gorm:"column:username;type:character varying(50);not null;comment:用户名" json:"username"` // 用户名
	Password  []uint8        `gorm:"column:password;type:bytea;not null;comment:密码" json:"password"`                  // 密码
	Nickname  string         `gorm:"column:nickname;type:character varying(100);not null;comment:昵称" json:"nickname"` // 昵称
	Avatar    string         `gorm:"column:avatar;type:character varying(255);not null;comment:头像" json:"avatar"`     // 头像
	Status    int32          `gorm:"column:status;type:integer;not null;default:1;comment:0 禁用  1正常" json:"status"`   // 0 禁用  1正常
	CreatedAt time.Time      `gorm:"column:created_at;type:timestamp without time zone;not null" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;type:timestamp without time zone;not null" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
}

// TableName Admins's table name
func (*Admins) TableName() string {
	return TableNameAdmins
}
