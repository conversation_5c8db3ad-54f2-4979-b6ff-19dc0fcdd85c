// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameMessages = "messages"

// Messages mapped from table <messages>
type Messages struct {
	ID        int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	Content   string         `gorm:"column:content;type:text;not null;comment:内容" json:"content"`                                                             // 内容
	Status    bool           `gorm:"column:status;type:boolean;not null;comment:是否已读" json:"status"`                                                          // 是否已读
	CreatedAt time.Time      `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time      `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName Messages's table name
func (*Messages) TableName() string {
	return TableNameMessages
}
