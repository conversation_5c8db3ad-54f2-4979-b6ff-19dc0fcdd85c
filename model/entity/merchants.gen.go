// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameMerchants = "merchants"

// Merchants mapped from table <merchants>
type Merchants struct {
	ID              int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt       time.Time      `gorm:"column:created_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt       time.Time      `gorm:"column:updated_at;type:timestamp(3) without time zone;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(3) without time zone;comment:删除时间" json:"deleted_at"`                                    // 删除时间
	Username        string         `gorm:"column:username;type:character varying(50);not null;index:idx_merchants_username,priority:1;comment:用户名" json:"username"` // 用户名
	Password        []uint8        `gorm:"column:password;type:bytea;not null;comment:密码" json:"password"`                                                          // 密码
	Status          int32          `gorm:"column:status;type:integer;not null;comment:状态 启用0 禁用1" json:"status"`                                                    // 状态 启用0 禁用1
	Secret          *string        `gorm:"column:secret;type:character varying(255);comment:Google密钥" json:"secret"`                                                // Google密钥
	Paysecret       *string        `gorm:"column:paysecret;type:character varying(255);comment:下发Google密钥" json:"paysecret"`                                        // 下发Google密钥
	AgentID         *int32         `gorm:"column:agent_id;type:integer;index:idx_merchants_agent_id,priority:1;comment:所属代理ID" json:"agent_id"`                     // 所属代理ID
	AgentName       *string        `gorm:"column:agent_name;type:character varying(255);comment:所属代理名" json:"agent_name"`                                           // 所属代理名
	Appkey          string         `gorm:"column:appkey;type:character varying(255);not null;comment:API密钥" json:"appkey"`                                          // API密钥
	Token           *string        `gorm:"column:token;type:character varying(255);comment:认证令牌" json:"token"`                                                      // 认证令牌
	Credits         *float64       `gorm:"column:credits;type:numeric(11,2);default:0.00;comment:信用额度(可透支手续费)" json:"credits"`                                      // 信用额度(可透支手续费)
	LimitAmount     *float64       `gorm:"column:limit_amount;type:numeric(10,2);comment:限制最低订单金额" json:"limit_amount"`                                             // 限制最低订单金额
	CommissionPrice *float64       `gorm:"column:commission_price;type:numeric(10,2);default:0.00;comment:固定按笔手续费" json:"commission_price"`                         // 固定按笔手续费
	Rate            float64        `gorm:"column:rate;type:numeric(11,2);not null;default:7;comment:汇率值" json:"rate"`                                               // 汇率值
	Floatratetype   float64        `gorm:"column:floatratetype;type:numeric(11,0);not null;default:2;comment:浮动类型 1百分比 2固定值" json:"floatratetype"`                  // 浮动类型 1百分比 2固定值
	Floatratedvalue float64        `gorm:"column:floatratedvalue;type:numeric(11,0);not null;comment:浮动值" json:"floatratedvalue"`                                   // 浮动值
	IsFloatdown     bool           `gorm:"column:is_floatdown;type:boolean;not null;comment:是否开启下浮" json:"is_floatdown"`                                            // 是否开启下浮
	Ordertimeout    float64        `gorm:"column:ordertimeout;type:numeric(11,0);not null;default:30;comment:订单超时时间(秒)" json:"ordertimeout"`                        // 订单超时时间(秒)
	Orderlocktime   float64        `gorm:"column:orderlocktime;type:numeric(11,0);not null;default:360;comment:订单锁定时间(秒)" json:"orderlocktime"`                     // 订单锁定时间(秒)
	Orderamount     float64        `gorm:"column:orderamount;type:numeric(11,2);not null;default:50;comment:大额订单阈值" json:"orderamount"`                             // 大额订单阈值
	GatherType      int16          `gorm:"column:gather_type;type:smallint;not null;default:1;comment:归集类型 1自动 2下发时" json:"gather_type"`                            // 归集类型 1自动 2下发时
}

// TableName Merchants's table name
func (*Merchants) TableName() string {
	return TableNameMerchants
}
