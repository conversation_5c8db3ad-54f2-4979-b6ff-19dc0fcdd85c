// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameBlockchain = "blockchain"

// Blockchain mapped from table <blockchain>
type Blockchain struct {
	ChainName        string     `gorm:"column:chain_name;type:character varying(50);primaryKey;comment:区块链名称(如:TRC,ERC,BSC)" json:"chain_name"`         // 区块链名称(如:TRC,ERC,BSC)
	NativeCurrency   string     `gorm:"column:native_currency;type:character varying(10);not null;comment:原生代币(如:TRX,ETH,BNB)" json:"native_currency"`  // 原生代币(如:TRX,ETH,BNB)
	IsActive         bool       `gorm:"column:is_active;type:boolean;not null;default:true;comment:是否启用该链" json:"is_active"`                            // 是否启用该链
	CreatedAt        *time.Time `gorm:"column:created_at;type:timestamp(3) without time zone;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt        *time.Time `gorm:"column:updated_at;type:timestamp(3) without time zone;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
	Network          *string    `gorm:"column:network;type:character varying(255);comment:网络类型" json:"network"`                                         // 网络类型
	Fee              float64    `gorm:"column:fee;type:numeric(10,5);not null" json:"fee"`
	MinCollectAmount *float64   `gorm:"column:min_collect_amount;type:numeric(10,5);default:10.0;comment:最小归集金额" json:"min_collect_amount"` // 最小归集金额
	KeepAmount       *float64   `gorm:"column:keep_amount;type:numeric(10,5);default:10.0;comment:预留手续费金额" json:"keep_amount"`              // 预留手续费金额
	BatchSize        *int32     `gorm:"column:batch_size;type:integer;default:20;comment:批处理大小" json:"batch_size"`                          // 批处理大小
	MaxRetry         *int32     `gorm:"column:max_retry;type:integer;default:3;comment:最大重试次数" json:"max_retry"`                            // 最大重试次数
	IntervalMinutes  *int32     `gorm:"column:interval_minutes;type:integer;default:60;comment:归集间隔（分钟）" json:"interval_minutes"`           // 归集间隔（分钟）
}

// TableName Blockchain's table name
func (*Blockchain) TableName() string {
	return TableNameBlockchain
}
