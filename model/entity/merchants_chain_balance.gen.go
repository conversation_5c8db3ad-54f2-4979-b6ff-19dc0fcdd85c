// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameMerchantsChainBalance = "merchants_chain_balance"

// MerchantsChainBalance mapped from table <merchants_chain_balance>
type MerchantsChainBalance struct {
	ID                int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	MerchantsID       int64     `gorm:"column:merchants_id;type:bigint;not null;index:idx_mcb_merchants_chain,priority:2;comment:商户ID" json:"merchants_id"`                                                  // 商户ID
	Balance           float64   `gorm:"column:balance;type:numeric(11,5);not null;default:0.00000;comment:主余额" json:"balance"`                                                                               // 主余额
	LockBalance       float64   `gorm:"column:lock_balance;type:numeric(11,5);not null;default:0.00000;comment:锁定余额" json:"lock_balance"`                                                                    // 锁定余额
	FeeBalance        float64   `gorm:"column:fee_balance;type:numeric(11,5);not null;default:0.00000;comment:手续费余额" json:"fee_balance"`                                                                     // 手续费余额
	GatherStatus      int32     `gorm:"column:gather_status;type:integer;not null;index:idx_mcb_gather_status,priority:1;comment:归集状态 0未执行 1等待 2执行中" json:"gather_status"`                                   // 归集状态 0未执行 1等待 2执行中
	NeedGatherBalance float64   `gorm:"column:need_gather_balance;type:numeric(11,5);not null;default:0.00000;comment:待归集余额" json:"need_gather_balance"`                                                     // 待归集余额
	LastUpdated       time.Time `gorm:"column:last_updated;type:timestamp(3) without time zone;not null;index:idx_mcb_last_updated,priority:1;default:CURRENT_TIMESTAMP;comment:最后更新时间" json:"last_updated"` // 最后更新时间
	ChainName         string    `gorm:"column:chain_name;type:character varying(50);not null;index:idx_mcb_merchants_chain,priority:1;comment:区块链ID" json:"chain_name"`                                      // 区块链ID
}

// TableName MerchantsChainBalance's table name
func (*MerchantsChainBalance) TableName() string {
	return TableNameMerchantsChainBalance
}
