package utils

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var (
	//Iplist         []string
	Rxf      = 0.00
	usdtRate = 7.0
	OkExApi  = "https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?t={time}&side=buy&quoteCurrency=CNY&baseCurrency=USDT"
	//base58Alphabet = []byte("**********************************************************")
)

func Get(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	request.Header.Add("User-Agent", "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; U8860 Build/HuaweiU8860) UC AppleWebKit/530+ (KHTML, like Gecko) Mobile Safari/530 ")
	//处理返回结果
	response, err := client.Do(request)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}

type OpexJson struct {
	Code int `json:"code"`
	Data []struct {
		BestOption bool   `json:"bestOption"`
		Payment    string `json:"payment"`
		Price      string `json:"price"`
	} `json:"data"`
	DetailMsg    string `json:"detailMsg"`
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Msg          string `json:"msg"`
}

func StringToFloat64(str string) float64 {
	num, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0.00
	}
	return num
}

func Decimal(num float64) float64 {
	num, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", num), 64)
	return num
}

func GetExchange() float64 {
	api := strings.ReplaceAll(OkExApi, "{time}", fmt.Sprint(time.Now().UnixNano()/1e6))
	jsons := Get(api)
	if jsons == "" {
		return usdtRate
	}
	var jsonData OpexJson
	err := json.Unmarshal([]byte(jsons), &jsonData)
	if err != nil {
		return usdtRate
	}
	if jsonData.Code != 0 {
		return usdtRate
	}
	r := StringToFloat64(jsonData.Data[0].Price)
	r = Decimal(r - r*Rxf)
	return r
}
