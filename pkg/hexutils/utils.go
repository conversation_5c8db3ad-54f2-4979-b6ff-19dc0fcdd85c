package hexutils

import (
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"math/big"
	"math/rand"
	"payAPI/internal/logx"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var (
	Iplist         = []string{}
	Rxf            = 0.00
	usdtRate       = 7.0
	OkExApi        = "https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?t={time}&side=buy&quoteCurrency=CNY&baseCurrency=USDT"
	base58Alphabet = []byte("**********************************************************")
)

func VerifyNotifyUrlFormat(url string) error {
	//判断url格式 是否是 http 或者 https 开头，判断地址是否包含://
	if !(len(url) > 7 && url[:7] == "http://" || url[:8] == "https://") {
		return fmt.Errorf("回调地址格式错误")
	}
	return nil
}

func StrToInt64(str string) int64 {
	i, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0
	}
	return i
}

func TimestampToTime(timestamp int64) time.Time {
	t := time.Unix(timestamp, 0)

	timeStr := t.Format("2006-01-02 15:04:05")
	// 字符串 -> 解析回 time.Time
	parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		logx.Error("解析时间失败:", err)
	}

	return parsedTime
}

func Md5(s string) string {
	srcCode := md5.Sum([]byte(s))
	code := fmt.Sprintf("%x", srcCode)
	return string(code)
}

func RandAllString(lenNum int) string {
	var CHARS = []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
		"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
		"1", "2", "3", "4", "5", "6", "7", "8", "9", "0"}
	str := strings.Builder{}
	length := len(CHARS)
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	time.Sleep(time.Microsecond / 1000)
	for i := 0; i < lenNum; i++ {
		l := CHARS[r.Intn(length)]
		str.WriteString(l)
	}
	return str.String()
}

func ValidateUsername(username string) error {
	// 正则表达式验证用户名格式，例如只允许字母和数字，长度在5到12之间
	pattern := `^[a-zA-Z0-9]{5,12}$`
	matched, _ := regexp.MatchString(pattern, username)
	if !matched {
		return fmt.Errorf("用户名格式不正确") // 返回错误信息，提示格式不正确
	}
	return nil // 格式正确，返回nil或继续后续逻辑
}

func ValidatePassword(password string) error {
	// 正则表达式验证密码格式，例如长度在5到20位之间
	pattern := `^.{5,20}$`
	matched, _ := regexp.MatchString(pattern, password)
	if !matched {
		return fmt.Errorf("密码格式不正确") // 返回错误信息，提示格式不正确
	}
	return nil // 格式正确，返回nil或继续后续逻辑
}

func base58Decode(input string) ([]byte, error) {
	result := big.NewInt(0)
	multiplier := big.NewInt(1)
	for i := len(input) - 1; i >= 0; i-- {
		index := -1
		for j, b := range base58Alphabet {
			if b == input[i] {
				index = j
				break
			}
		}
		if index == -1 {
			return nil, fmt.Errorf("解码出错，发现无效字符: %c", input[i])
		}
		temp := big.NewInt(int64(index))
		temp.Mul(temp, multiplier)
		result.Add(result, temp)
		multiplier.Mul(multiplier, big.NewInt(58))
	}

	decoded := result.Bytes()
	return decoded, nil
}

func ValidateAddress(address string) (bool, error) {
	decoded, err := base58Decode(address)
	if err != nil {
		return false, err
	}

	if len(decoded) != 25 || decoded[0] != 0x41 {
		return false, nil
	}

	hash := sha256.Sum256(decoded[:21])
	hash = sha256.Sum256(hash[:])

	for i := 0; i < 4; i++ {
		if decoded[21+i] != hash[i] {
			return false, nil
		}
	}

	return true, nil
}

func IsTRC20Address(str string) bool {
	address := str
	isValid, err := ValidateAddress(address)
	if err != nil {
		fmt.Println("错误:", err)
		return false
	}
	return isValid
}

func IsERC20Address(addr string) bool {
	// ERC-20 addresses are 42 characters long, with "0x" prefix
	if len(addr) != 42 || addr[:2] != "0x" {
		return false
	}

	// Check that the remaining 40 characters are valid hex digits
	match, _ := regexp.MatchString("^0x[0-9a-fA-F]{40}$", addr)
	return match
}
