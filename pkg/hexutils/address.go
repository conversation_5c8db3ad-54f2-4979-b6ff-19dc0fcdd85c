package hexutils

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"github.com/mr-tron/base58"
)

func EncodeBytesAddress(input []byte) string {
	hash0 := sha256.Sum256(input)
	hash1 := sha256.Sum256(hash0[:])
	checksum := hash1[:4]
	output := bytes.Join([][]byte{input, checksum}, []byte{})

	b58Addr := base58.Encode(output)

	return b58Addr
}

func EncodeHexAddress(hexAddr string) (string, error) {
	input, err := hex.DecodeString(hexAddr)
	if err != nil {
		return "", err
	}
	return EncodeBytesAddress(input), nil
}

func DecodeBase58Address(b58Addr string) (string, error) {
	b58, err := base58.Decode(b58Addr)
	if err != nil {
		return "", fmt.Errorf("base58解码失败: %v", err)
	}

	// 检查解码后的字节数组长度是否足够
	if len(b58) < 21 {
		return "", fmt.Errorf("地址格式错误")
	}

	hexAddr := hex.EncodeToString(b58[:21])
	return hexAddr, nil
}

func Base58ToEthAddress(b58Addr string) (common.Address, error) {
	hexAddr, err := DecodeBase58Address(b58Addr)
	if err != nil {
		return common.Address{}, err
	}
	return common.HexToAddress(hexAddr[2:]), nil
}

func Base58FromEthAddress(ethAddr common.Address) string {
	ethBytes := ethAddr.Bytes()
	tronBytes := bytes.Join([][]byte{[]byte{0x41}, ethBytes}, []byte{})
	return EncodeBytesAddress(tronBytes)
}

func DecodeABI(abistr string) (string, string, string, error) {
	selector := ""
	toAddress := ""
	amountHex := ""

	if len(abistr) >= 136 {
		//提取函数选择器
		selector = abistr[:8]

		// 提取收款人地址
		toAddress = abistr[8:72]

		// 提取发送金额
		amountHex = abistr[72:136]
	} else {
		return "", "", "", errors.New("abistr too short")
	}

	bytes, err := hex.DecodeString(amountHex)
	if err != nil {
		fmt.Println("ABIerr:" + err.Error())
		return "", "", "", err
	}
	var val big.Int
	val.SetBytes(bytes)
	return selector, toAddress, val.String(), nil
}
