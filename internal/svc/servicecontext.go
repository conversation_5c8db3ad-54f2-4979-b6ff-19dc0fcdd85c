package svc

import (
	"fmt"
	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"log"
	"payAPI/dal"
	"payAPI/internal/config"
	"time"
)

// ServiceContext 服务上下文，用于存储服务级别的依赖
type ServiceContext struct {
	Config   *config.Config
	DB       *gorm.DB
	DBEngine *gorm.DB
	Query    *dal.Query
}

// NewServiceContext 创建服务上下文
func NewServiceContext(c *config.Config) (*ServiceContext, error) {
	// postGre
	PostgresSQLConf := c.PostgresSQLConf
	var db *gorm.DB
	var query *dal.Query
	var err error
	db, err = CreteDbClient(PostgresSQLConf)
	if err != nil {
		db = nil
	} else {
		// 创建 dal 查询对象
		query = dal.Use(db)
	}
	return &ServiceContext{
		Config:   c,
		Query:    query,
		DBEngine: db,
	}, nil
}

func dbMigrate(db *gorm.DB) {
	dbs := db.Session(&gorm.Session{
		Logger: db.Logger.LogMode(logger.Warn),
	})
	err := dbs.AutoMigrate(
	//&entity.User{},
	)
	if err != nil {
		log.Printf("failed auto migrate db %v", err.Error())
	}
}

func CreteDbClient(postGreConf config.PostgresSQLConf) (*gorm.DB, error) {
	datasource := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable TimeZone=Asia/Shanghai",
		postGreConf.Host, postGreConf.Port, postGreConf.User, postGreConf.Password, postGreConf.Database)
	db, err := gorm.Open(postgres.Open(datasource), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   postGreConf.Gorm.TablePrefix,   // such as: prefix_tableName
			SingularTable: postGreConf.Gorm.SingularTable, // such as zero_user, not zero_users
		},
	})
	if err != nil {
		log.Printf("create postGre db failed, err: %v", err)
		return nil, err
	}

	//在这配置自动迁移
	//err = db.AutoMigrate(&entity.Record{})
	//if err != nil {
	//	logx.Errorf("automigrate table failed, err: %v", err)
	//} else {
	//	logx.Info("数据迁移成功！！！")
	//}

	log.Printf("init postGre client instance success.")

	sqlDB, err := db.DB()
	if err != nil {
		log.Printf("postGre set connection pool failed, err: %v.", err)
		return nil, err
	}
	sqlDB.SetMaxOpenConns(postGreConf.Gorm.MaxOpenConns)
	sqlDB.SetMaxIdleConns(postGreConf.Gorm.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(postGreConf.Gorm.ConnMaxLifetime) * time.Second)
	dbMigrate(db)
	return db, nil
}
