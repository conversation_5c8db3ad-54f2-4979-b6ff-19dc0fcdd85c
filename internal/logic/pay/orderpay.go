package pay

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"payAPI/internal/logx"
	"payAPI/internal/service"
	"payAPI/internal/svc"
	"payAPI/internal/types"
	"payAPI/internal/websocket"
	"payAPI/model/entity"
	"payAPI/pkg/hexutils"
	"payAPI/pkg/utils"
	"strings"
	"time"

	mathrand "math/rand"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"golang.org/x/crypto/chacha20poly1305"
	"golang.org/x/crypto/pbkdf2"
	"gorm.io/gorm"
)

type OrderPayLogic struct {
	ctx    *fiber.Ctx
	svcCtx *svc.ServiceContext
}

func NewLogic(ctx *fiber.Ctx, svcCtx *svc.ServiceContext) *OrderPayLogic {
	return &OrderPayLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// BindAddress  绑定用户地址
func (l *OrderPayLogic) BindAddress(req *types.BindAddressRequest) error {
	// 验证地址格式
	if !hexutils.IsTRC20Address(req.TrcAddress) {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "TRC地址格式错误",
		})
	}

	if !hexutils.IsERC20Address(req.ErcAddress) {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "ERC地址格式错误",
		})
	}

	// 验证商户是否存在
	merchant, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "商户不存在",
		})
	}

	// 检查用户ID是否已绑定
	existingBinding, _ := l.svcCtx.Query.AddressCore.Where(
		l.svcCtx.Query.AddressCore.UserID.Eq(req.UserID),
		l.svcCtx.Query.AddressCore.MerchantsID.Eq(req.MerchantsID),
	).First()

	if existingBinding != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "用户ID已存在绑定地址",
		})
	}

	// 验证TRC和ERC地址是否已被使用
	trcAsset, _ := l.svcCtx.Query.AddressChain.Where(
		l.svcCtx.Query.AddressChain.Address.Eq(req.TrcAddress),
		l.svcCtx.Query.AddressChain.ChainName.Eq("TRC20"),
	).First()

	ercAsset, _ := l.svcCtx.Query.AddressChain.Where(
		l.svcCtx.Query.AddressChain.Address.Eq(req.ErcAddress),
		l.svcCtx.Query.AddressChain.ChainName.Eq("ERC20"),
	).First()

	if trcAsset != nil || ercAsset != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "地址已被使用",
		})
	}

	// 开始事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "系统错误",
		})
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 生成虚拟私钥（用于绑定外部地址）
	virtualPrivateKey := hexutils.Md5(req.UserID + req.TrcAddress + req.ErcAddress)

	// 创建地址核心记录
	falseVal := false
	zeroInt32 := int32(0)
	zeroInt64 := int64(0)

	addressCore := &entity.AddressCore{
		PrivateKey:  virtualPrivateKey, // 虚拟私钥，表示这是绑定的外部地址
		UserID:      &req.UserID,
		MerchantsID: &merchant.ID,
		Lock:        &falseVal,
		Locktime:    &zeroInt64,
		Status:      &zeroInt32,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = tx.Create(addressCore).Error
	if err != nil {
		tx.Rollback()
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "创建地址绑定失败",
		})
	}

	// 获取生成的ID
	addressCoreID := addressCore.ID

	// 创建TRC地址资产记录
	trcAssetRecord := &entity.AddressChain{
		AddressCoreID:    addressCoreID,
		ChainName:        "TRC20",
		Address:          req.TrcAddress,
		NativeBalance:    0,
		UsdtBalance:      0,
		TransactionsNums: 0,
		LockStatus:       false,
		Status:           0,
		MerchantsID:      &merchant.ID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = tx.Create(trcAssetRecord).Error
	if err != nil {
		tx.Rollback()
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "创建TRC地址记录失败",
		})
	}

	// 创建ERC地址资产记录
	ercAssetRecord := &entity.AddressChain{
		AddressCoreID:    addressCoreID,
		ChainName:        "ERC",
		Address:          req.ErcAddress,
		NativeBalance:    0,
		UsdtBalance:      0,
		TransactionsNums: 0,
		LockStatus:       false,
		Status:           0,
		MerchantsID:      &merchant.ID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = tx.Create(ercAssetRecord).Error
	if err != nil {
		tx.Rollback()
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "创建ERC地址记录失败",
		})
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "提交事务失败",
		})
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "绑定用户地址成功",
		"data": map[string]interface{}{
			"user_id":     req.UserID,
			"trc_address": req.TrcAddress,
			"erc_address": req.ErcAddress,
			"binding_id":  addressCoreID,
		},
	})
}

// UpdateAddress  更新用户地址
func (l *OrderPayLogic) UpdateAddress(req *types.UpdateAddressRequest) error {
	// 验证商户是否存在
	_, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "商户不存在",
		})
	}

	// 查找现有的用户地址绑定
	addressCore, err := l.svcCtx.Query.AddressCore.Where(
		l.svcCtx.Query.AddressCore.UserID.Eq(req.UserID),
		l.svcCtx.Query.AddressCore.MerchantsID.Eq(req.MerchantsID),
	).First()

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 404,
			"msg":  "用户地址绑定不存在",
		})
	}

	// 验证地址格式（如果提供了新地址）
	if req.TrcAddress != "" && !hexutils.IsTRC20Address(req.TrcAddress) {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "TRC地址格式错误",
		})
	}

	if req.ErcAddress != "" && !hexutils.IsERC20Address(req.ErcAddress) {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "ERC地址格式错误",
		})
	}

	// 开始事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "系统错误",
		})
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新TRC地址（如果提供了新地址）
	if req.TrcAddress != "" {
		// 检查新地址是否已被使用
		existingTrc, _ := l.svcCtx.Query.AddressChain.Where(
			l.svcCtx.Query.AddressChain.Address.Eq(req.TrcAddress),
			l.svcCtx.Query.AddressChain.ChainName.Eq("TRC"),
		).First()

		if existingTrc != nil && existingTrc.AddressCoreID != addressCore.ID {
			tx.Rollback()
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 400,
				"msg":  "TRC地址已被其他用户使用",
			})
		}

		err = tx.Model(&entity.AddressChain{}).Where(
			"address_core_id = ? AND chain_name = ?",
			addressCore.ID,
			"TRC",
		).Updates(map[string]interface{}{
			"address":    req.TrcAddress,
			"updated_at": time.Now(),
		}).Error

		if err != nil {
			tx.Rollback()
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 500,
				"msg":  "更新TRC地址失败",
			})
		}
	}

	// 更新ERC地址（如果提供了新地址）
	if req.ErcAddress != "" {
		// 检查新地址是否已被使用
		existingErc, _ := l.svcCtx.Query.AddressChain.Where(
			l.svcCtx.Query.AddressChain.Address.Eq(req.ErcAddress),
			l.svcCtx.Query.AddressChain.ChainName.Eq("ERC"),
		).First()

		if existingErc != nil && existingErc.AddressCoreID != addressCore.ID {
			tx.Rollback()
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 400,
				"msg":  "ERC地址已被其他用户使用",
			})
		}

		err = tx.Model(&entity.AddressChain{}).Where(
			"address_core_id = ? AND chain_name = ?",
			addressCore.ID,
			"ERC",
		).Updates(map[string]interface{}{
			"address":    req.ErcAddress,
			"updated_at": time.Now(),
		}).Error

		if err != nil {
			tx.Rollback()
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 500,
				"msg":  "更新ERC地址失败",
			})
		}
	}

	// 更新地址核心记录的更新时间
	err = tx.Model(&entity.AddressCore{}).Where("id = ?", addressCore.ID).Update("updated_at", time.Now()).Error
	if err != nil {
		tx.Rollback()
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "更新地址核心记录失败",
		})
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "提交事务失败",
		})
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "更新用户地址成功",
		"data": map[string]interface{}{
			"user_id":     req.UserID,
			"trc_address": req.TrcAddress,
			"erc_address": req.ErcAddress,
			"binding_id":  addressCore.ID,
		},
	})
}

// UpdateCallbackURL  更新回调地址
func (l *OrderPayLogic) UpdateCallbackURL(req *types.UpdateCallbackURLRequest) error {
	// 验证回调URL格式
	if req.CallbackURL == "" {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "回调地址不能为空",
		})
	}

	// 验证商户是否存在
	_, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "商户不存在",
		})
	}

	// 查找订单
	order, err := l.svcCtx.Query.Orders.Where(
		l.svcCtx.Query.Orders.OrderID.Eq(req.OrderID),
		l.svcCtx.Query.Orders.MerchantsID.Eq(req.MerchantsID),
	).First()

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 404,
			"msg":  "订单不存在",
		})
	}

	// 检查订单状态，只有未支付的订单才允许更新回调地址
	if order.Status != 0 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "只有未支付的订单才能更新回调地址",
		})
	}

	// 更新回调地址
	_, err = l.svcCtx.Query.Orders.Where(
		l.svcCtx.Query.Orders.OrderID.Eq(req.OrderID),
		l.svcCtx.Query.Orders.MerchantsID.Eq(req.MerchantsID),
	).Updates(&entity.Orders{
		CallbackURL: req.CallbackURL,
		UpdatedAt:   time.Now(),
	})

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "更新回调地址失败",
		})
	}

	// 同时更新对应的地址核心记录的回调地址（如果存在）
	if order.AddressID > 0 {
		l.svcCtx.Query.AddressCore.Where(
			l.svcCtx.Query.AddressCore.ID.Eq(order.AddressID),
		).Updates(&entity.AddressCore{
			CallbackURL: &req.CallbackURL,
			UpdatedAt:   time.Now(),
		})
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "更新回调地址成功",
		"data": map[string]interface{}{
			"order_id":     req.OrderID,
			"merchants_id": req.MerchantsID,
			"callback_url": req.CallbackURL,
		},
	})
}

// ReceivePayCancel  收款订单取消
func (l *OrderPayLogic) ReceivePayCancel(req *types.ReceivePayCancelRequest) error {
	// 验证商户是否存在
	_, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "商户不存在",
		})
	}

	// 查找订单
	order, err := l.svcCtx.Query.Orders.Where(
		l.svcCtx.Query.Orders.OrderID.Eq(req.OrderID),
		l.svcCtx.Query.Orders.MerchantsID.Eq(req.MerchantsID),
	).First()

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 404,
			"msg":  "订单不存在",
		})
	}

	// 检查订单状态，只有未支付的订单才能取消
	if order.Status != 0 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "只有未支付的订单才能取消",
		})
	}

	// 开始事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "系统错误",
		})
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新订单状态为已取消 (2表示已取消)
	err = tx.Model(&entity.Orders{}).Where(
		"order_id = ? AND merchants_id = ?",
		req.OrderID,
		req.MerchantsID,
	).Updates(map[string]interface{}{
		"status":     2,
		"updated_at": time.Now(),
	}).Error

	if err != nil {
		tx.Rollback()
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "取消订单失败",
		})
	}

	// 释放锁定的地址
	if order.AddressID > 0 {
		// 删除地址锁定记录
		err = tx.Where("address_id = ? AND islock = ?", order.AddressID, true).Delete(&entity.LockAddress{}).Error
		if err != nil {
			logx.Error("删除地址锁定记录失败: %v", err)
		}

		// 更新地址核心状态为未锁定
		falseVal := false
		zeroVal := int64(0)
		err = tx.Model(&entity.AddressCore{}).Where("id = ?", order.AddressID).Updates(map[string]interface{}{
			"lock":       &falseVal,
			"locktime":   &zeroVal,
			"updated_at": time.Now(),
		}).Error

		if err != nil {
			logx.Error("更新地址锁定状态失败: %v", err)
		}
	}

	// 释放商户锁定的余额
	balanceService := service.NewBalanceService(l.svcCtx)
	err = balanceService.UnlockBalance(req.MerchantsID, order.ChainName, order.Amount)
	if err != nil {
		logx.Error("释放商户余额失败: %v", err)
		// 余额释放失败不应该阻止订单取消，所以只记录日志
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "提交事务失败",
		})
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "收款订单取消成功",
		"data": map[string]interface{}{
			"order_id":     req.OrderID,
			"merchants_id": req.MerchantsID,
			"status":       2,
		},
	})
}

// IssuedPayCancel  下发订单取消
func (l *OrderPayLogic) IssuedPayCancel(req *types.IssuedPayCancelRequest) error {
	// 验证商户是否存在
	_, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "商户不存在",
		})
	}

	// 查找下发订单
	order, err := l.svcCtx.Query.PlaceOrder.Where(
		l.svcCtx.Query.PlaceOrder.OrderID.Eq(req.OrderID),
		l.svcCtx.Query.PlaceOrder.MerchantsID.Eq(req.MerchantsID),
	).First()

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 404,
			"msg":  "下发订单不存在",
		})
	}

	// 检查订单状态，只有未处理的订单才能取消 (0表示未处理)
	if order.Status == nil || *order.Status != 0 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 400,
			"msg":  "只有未处理的下发订单才能取消",
		})
	}

	// 开始事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "系统错误",
		})
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新下发订单状态为已取消 (4表示已取消)
	cancelStatus := int32(4)
	err = tx.Model(&entity.PlaceOrder{}).Where(
		"order_id = ? AND merchants_id = ?",
		req.OrderID,
		req.MerchantsID,
	).Updates(map[string]interface{}{
		"status":     &cancelStatus,
		"updated_at": time.Now(),
	}).Error

	if err != nil {
		tx.Rollback()
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "取消下发订单失败",
		})
	}

	// 释放锁定的商户余额 - 下发订单的锁定余额需要释放
	var chainName string
	if order.ChainName != nil {
		chainName = *order.ChainName
	} else {
		chainName = "TRC20" // 默认链
	}

	// 计算总金额（订单金额 + 手续费）
	// 获取该链的手续费配置
	blockchain, err := l.svcCtx.Query.Blockchain.Where(
		l.svcCtx.Query.Blockchain.ChainName.Eq(chainName),
		l.svcCtx.Query.Blockchain.IsActive.Is(true),
	).First()

	totalAmount := order.Amount
	if err == nil && blockchain != nil {
		totalAmount += blockchain.Fee
	}

	// 释放锁定的余额
	err = tx.Model(&entity.MerchantsChainBalance{}).Where(
		"merchants_id = ? AND chain_name = ?",
		req.MerchantsID,
		chainName,
	).Updates(map[string]interface{}{
		"balance":      gorm.Expr("balance + lock_balance"),
		"lock_balance": gorm.Expr("lock_balance - ?", totalAmount),
		"last_updated": time.Now(),
	}).Error

	if err != nil {
		logx.Error("释放下发订单锁定余额失败: %v", err)
		// 余额释放失败不应该阻止订单取消，记录日志继续
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "提交事务失败",
		})
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "下发订单取消成功",
		"data": map[string]interface{}{
			"order_id":     req.OrderID,
			"merchants_id": req.MerchantsID,
			"status":       4,
		},
	})
}

// ReceivePay 收款下单
func (l *OrderPayLogic) ReceivePay(req *types.ReceivePayRequest) error {
	// 检查订单是否已存在
	count, _ := l.svcCtx.Query.Orders.Where(l.svcCtx.Query.Orders.MerchantOrderID.Eq(req.MerchantOrderId)).Count()
	if count > 0 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "订单已存在",
			"data": nil,
		})
	}

	if req.Amount < 10 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "最小金额为 10 USDT",
			"data": nil,
		})
	}

	tm := time.Now()
	mathrand.Seed(time.Now().UnixNano())
	randomNumber := mathrand.Intn(9000) + 1000
	var address string
	var orderType string
	var orderId string

	switch req.Chain {
	case "ERC20", "Eth":
		orderType = "ERC20"
		orderId = "CE" + tm.Format("060102150405") + cast.ToString(randomNumber)
	case "Bsc", "BEP20", "Bnb":
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "BSC通道已关闭,请联系客服开启",
		})
	default:
		orderType = "TRC20"
		orderId = "CT" + tm.Format("060102150405") + cast.ToString(randomNumber)
	}

	nMoney := req.Amount
	OKXExchangeRate := utils.GetExchange()
	//rmb := 0.00
	rate := 0.00

	merchantsChainConfigs, err := l.svcCtx.Query.MerchantsChainConfig.Where(l.svcCtx.Query.MerchantsChainConfig.MerchantsID.Eq(req.MerchantsID)).Find()
	merchants, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "商户链配置不存在",
			"data": nil,
		})
	}

	// 处理汇率和金额计算（只取第一个配置或使用默认配置）
	if len(merchantsChainConfigs) > 0 {
		val := merchantsChainConfigs[0] // 使用第一个配置
		if val.Ratetype == 1 {          //固定汇率
			rate, _ = decimal.NewFromFloat(merchants.Rate).Truncate(2).Float64()
		} else {                              //浮动汇率
			if merchants.Floatratetype == 1 { //百分比
				der := decimal.NewFromFloat(OKXExchangeRate)
				dej, _ := decimal.NewFromFloat(OKXExchangeRate).Mul(decimal.NewFromFloat(merchants.Floatratedvalue)).Div(decimal.NewFromFloat(100)).RoundCeil(2).Float64()
				rate, _ = der.Sub(decimal.NewFromFloat(dej)).RoundCeil(2).Float64()
			} else { //固定值
				der := decimal.NewFromFloat(OKXExchangeRate)
				dej := decimal.NewFromFloat(merchants.Floatratedvalue)
				rate, _ = der.Sub(dej).RoundCeil(2).Float64()
			}
		}
	} else {
		// 如果没有配置，使用默认汇率
		rate = OKXExchangeRate
	}

	if req.Currency == "CNY" {
		a := decimal.NewFromFloat(req.Amount)
		b := decimal.NewFromFloat(rate)
		c := a.Div(b)
		nMoney, _ = c.RoundCeil(2).Float64()
	} else {
		a := decimal.NewFromFloat(nMoney)
		nMoney, _ = a.RoundCeil(2).Float64()
	}

	if nMoney < 10 && req.MerchantsID != 1 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "最小金额为10U",
		})
	}

	// 创建服务实例
	balanceService := service.NewBalanceService(l.svcCtx)
	feeService := service.NewFeeService(l.svcCtx)

	// 检查商户余额是否充足
	sufficient, err := balanceService.CheckSufficientBalance(req.MerchantsID, orderType, nMoney)
	if err != nil {
		logx.Error("检查商户余额失败", logx.String("error", err.Error()))
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "余额检查失败",
		})
	}

	if !sufficient {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户余额不足",
		})
	}

	// 计算并检查手续费
	estimatedFee, feeConfig := feeService.EstimateFee(orderType, nMoney)
	sufficientFee, err := balanceService.CheckSufficientFeeBalance(req.MerchantsID, orderType, estimatedFee)
	if err != nil {
		logx.Error("检查手续费余额失败", logx.String("error", err.Error()))
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "手续费余额检查失败",
		})
	}
	logx.Info("手续费检查结果: %v", sufficientFee)

	if !sufficientFee && estimatedFee > 0 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "手续费余额不足",
			"data": map[string]interface{}{
				"required_fee": estimatedFee,
				"fee_config":   feeConfig,
			},
		})
	}

	logx.Info("余额和手续费检查通过",
		logx.Int64("merchants_id", req.MerchantsID),
		logx.String("chain", orderType),
		logx.Float64("order_amount", nMoney),
		logx.Float64("estimated_fee", estimatedFee))

	// 开始数据库事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		logx.Error("开始事务失败: %v", tx.Error)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "系统错误",
			"data": nil,
		})
	}

	// 确保事务在函数结束时处理
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logx.Error("事务panic回滚: %v", r)
		}
	}()

	var trc string
	var erc string
	var addressId int64

	// 如果用户提供了UserId，查找用户绑定的地址
	if req.UserId != "" {
		// TODO: 根据UserId查找用户绑定的地址
		// 这里需要根据实际的用户地址表结构来查询
		logx.Info("使用用户绑定地址，用户ID: %s", req.UserId)
	} else {
		// 获取可用地址（在事务中执行）
		trc, erc, addressId, err = l.GetTrueAddr(tx, merchants.ID, cast.ToString(nMoney), orderType)
		logx.Info("获取可用地址，TRC: %s, ERC: %s, 错误: %v", trc, erc, err)

		if err != nil || trc == "" || erc == "" {
			tx.Rollback()
			logx.Error("获取地址失败，事务回滚: %v", err)
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 500,
				"msg":  "创建订单失败",
			})
		}
	}

	// 根据链类型选择地址
	if req.Chain == "Erc" || req.Chain == "Eth" || req.Chain == "Bsc" || req.Chain == "Bep" || req.Chain == "Bnb" {
		address = erc
	} else {
		address = trc
	}

	// 创建订单记录（在事务中执行）
	order := &entity.Orders{
		MerchantsID:     req.MerchantsID,
		OrderID:         orderId,
		MerchantOrderID: &req.MerchantOrderId,
		PayAddress:      "",  // 付款地址在用户支付时填写
		PayScore:        "0", // 默认风险评分
		Address:         address,
		AddressID:       addressId,
		Amount:          nMoney,
		Status:          0,   // 0:未支付
		CallbackStatus:  nil, // 初始未回调
		CallbackURL:     req.CallbackURL,
		CallbackNum:     0, // 初始回调次数为0
		CallbackResult:  nil,
		Rate:            rate,
		RealAmount:      nMoney,    // 实收金额等于订单金额
		ChainName:       orderType, // 使用链类型作为链名称
		ReturnURL:       &req.ReturnURL,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	err = tx.Create(order).Error
	if err != nil {
		tx.Rollback()
		logx.Error("创建订单记录失败，事务回滚: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "创建订单失败",
			"data": nil,
		})
	}

	// 根据订单金额决定锁定时间 (适配新表结构)
	var lockTime int64
	if nMoney > merchants.Orderamount {
		// 大额订单：使用配置的锁定时间
		lockTime = int64(merchants.Orderlocktime) // 新表结构中Orderlocktime已经是秒
	} else {
		// 小额订单：使用较短的锁定时间 (原逻辑的一半)
		lockTime = int64(merchants.Orderlocktime) / 2
		if lockTime < 300 { // 最少5分钟
			lockTime = 300
		}
	}

	// 锁定地址（在事务中执行）
	success := l.DoLockAddr(tx, addressId, nMoney, lockTime)
	if !success {
		tx.Rollback()
		logx.Error("锁定地址失败，事务回滚，addressId: %d", addressId)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "创建订单失败",
			"data": nil,
		})
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		logx.Error("提交事务失败: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "创建订单失败",
			"data": nil,
		})
	}

	// 获取收银台URL配置
	cashierUrl := l.svcCtx.Config.Cashier.Url

	// 返回数据格式与原项目保持一致
	data := make(map[string]string)
	data["trxAddress"] = address                                                    // 收款地址 (与原项目字段名一致)
	data["orderId"] = orderId                                                       // 系统订单号
	data["merchantOrderId"] = req.MerchantOrderId                                   // 商户订单号 (与原项目字段名一致)
	data["amount"] = cast.ToString(nMoney)                                          // 订单金额
	data["cashierUrl"] = cashierUrl + "?orderId=" + orderId + "&chain=" + orderType // 收银台URL

	// 锁定商户余额
	err = balanceService.LockBalance(req.MerchantsID, orderType, nMoney)
	if err != nil {
		logx.Error("锁定商户余额失败", logx.String("error", err.Error()))
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "锁定余额失败",
		})
	}

	// 扣除手续费（如果有的话）
	if estimatedFee > 0 {
		err = feeService.DeductFeeBalance(req.MerchantsID, orderType, estimatedFee)
		if err != nil {
			// 如果手续费扣除失败，需要回滚余额锁定
			balanceService.UnlockBalance(req.MerchantsID, orderType, nMoney)
			logx.Error("扣除手续费失败", logx.String("error", err.Error()))
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 201,
				"msg":  "扣除手续费失败",
			})
		}

		// 添加手续费信息到返回数据
		data["fee_amount"] = fmt.Sprintf("%.6f", estimatedFee)
		data["fee_config"] = fmt.Sprintf("%+v", feeConfig)
	}

	logx.Info("订单创建成功，余额和手续费处理完成",
		logx.String("order_id", orderId),
		logx.Int64("merchants_id", req.MerchantsID),
		logx.Float64("locked_amount", nMoney),
		logx.Float64("fee_deducted", estimatedFee))

	// 调用ChainEye API添加地址监控
	go l.addChainEyeMonitoring(address, orderType)

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "创建订单成功",
		"data": data,
	})
}

// ReceivePayQuery 收款下单查询
func (l *OrderPayLogic) ReceivePayQuery(req *types.ReceivePayQueryRequest) error {
	// 根据订单ID和商户ID查询订单
	order, err := l.svcCtx.Query.Orders.Where(
		l.svcCtx.Query.Orders.OrderID.Eq(req.OrderID),
		l.svcCtx.Query.Orders.MerchantsID.Eq(req.MerchantsID),
	).First()

	if err != nil {
		logx.Error("查询订单失败: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 404,
			"msg":  "订单不存在",
			"data": nil,
		})
	}

	// 处理结束时间 - 如果订单已完成或已取消，使用更新时间作为结束时间
	var etime interface{}
	if order.Status == 1 || order.Status == 2 { // 1:已支付, 2:已取消
		etime = order.UpdatedAt.Unix()
	} else {
		etime = nil
	}

	// 构造返回数据
	data := map[string]interface{}{
		"createdAt":       order.CreatedAt.Unix(), // 创建时间戳
		"endAt":           etime,                  // 结束时间戳
		"orderId":         order.OrderID,          // 系统订单号
		"merchantOrderId": "",                     // 商户订单号
		"status":          order.Status,           // 订单状态
		"trxAddress":      order.Address,          // 收款地址
	}

	// 处理商户订单号 - 可能为空指针
	if order.MerchantOrderID != nil {
		data["merchantOrderId"] = *order.MerchantOrderID
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "查询成功",
		"data": data,
	})
}

// IssuedPay  下发下单
func (l *OrderPayLogic) IssuedPay(req *types.IssuedPayRequest) error {
	// BSC通道检查
	if req.Chain == "Bsc" {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "BSC通道已关闭,请联系客服开启",
		})
	}

	// 商户验证
	mch, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil || mch == nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户不存在",
		})
	}

	// 商户状态检查
	if mch.Status == 1 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户账号已冻结",
		})
	}

	// 地址格式验证和链类型自动检测
	if !hexutils.IsTRC20Address(req.Address) && !hexutils.IsERC20Address(req.Address) {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "请提交正确的地址信息",
		})
	}

	// 获取所有可用的区块链配置
	blockchains, err := l.svcCtx.Query.Blockchain.Where(l.svcCtx.Query.Blockchain.IsActive.Is(true)).Find()
	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "查询区块链配置失败",
		})
	}

	// 建立链类型映射关系和可用链列表
	chainMapping := make(map[string]string)     // 用户输入 -> 数据库标准名称
	chainFeeMapping := make(map[string]float64) // 链名称 -> 手续费
	availableChains := make([]string, 0)

	for _, blockchain := range blockchains {
		// 建立小写映射关系，方便匹配
		lowerChainName := strings.ToLower(blockchain.ChainName)
		chainMapping[lowerChainName] = blockchain.ChainName
		chainFeeMapping[blockchain.ChainName] = blockchain.Fee
		availableChains = append(availableChains, blockchain.ChainName)
	}

	// 自动检测链类型
	chain := req.Chain
	if chain != "Bsc" {
		if !hexutils.IsERC20Address(req.Address) {
			chain = "Trc"
		} else {
			chain = "Erc"
		}
	}

	// 验证链类型并获取标准链名称
	userChain := strings.ToLower(chain)
	chainNameInDB, exists := chainMapping[userChain]
	if !exists {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "请提交正确的链信息,支持的链: " + strings.Join(availableChains, ","),
		})
	}

	// 商户限额检查
	if mch.LimitAmount != nil && *mch.LimitAmount != 0 {
		if req.Amount < *mch.LimitAmount {
			return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": 201,
				"msg":  fmt.Sprintf("最小金额为 %.2f U", *mch.LimitAmount),
			})
		}
	}

	// 获取该链的手续费
	fee, feeExists := chainFeeMapping[chainNameInDB]
	if !feeExists {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "该链手续费配置不存在",
		})
	}

	totalAmount := req.Amount + fee

	// 获取商户在该链上的余额信息
	chainBalance, err := l.svcCtx.Query.MerchantsChainBalance.Where(
		l.svcCtx.Query.MerchantsChainBalance.MerchantsID.Eq(req.MerchantsID),
		l.svcCtx.Query.MerchantsChainBalance.ChainName.Eq(chainNameInDB),
	).First()

	if err != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户在该链上无余额记录",
		})
	}
	logx.Info("chainBalance.Balance: %v", chainBalance.Balance)
	logx.Info("chainBalance.Balance: %v", totalAmount)
	// 余额检查
	if chainBalance.Balance < totalAmount {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户余额不足",
		})
	}

	// 获取汇率配置 - 优先从merchants_chain_config表获取
	var rate float64
	var rateType float64

	// 查找该链的配置
	chainConfig, err := l.svcCtx.Query.MerchantsChainConfig.Where(
		l.svcCtx.Query.MerchantsChainConfig.MerchantsID.Eq(req.MerchantsID),
		l.svcCtx.Query.MerchantsChainConfig.ChainName.Eq(chainNameInDB),
	).First()

	if err == nil && chainConfig != nil {
		// 使用链特定配置
		rateType = chainConfig.Ratetype
	} else {
		// 使用商户全局配置 - 通过floatratetype判断汇率类型
		rateType = mch.Floatratetype
	}

	// 汇率计算
	OKXExchangeRate := utils.GetExchange()

	if rateType == 1 { // 固定汇率
		rate = mch.Rate
	} else {                        // 浮动汇率
		if mch.Floatratetype == 1 { // 百分比
			der := decimal.NewFromFloat(OKXExchangeRate)
			dej, _ := decimal.NewFromFloat(OKXExchangeRate).Mul(decimal.NewFromFloat(mch.Floatratedvalue)).Div(decimal.NewFromFloat(100)).RoundCeil(2).Float64()
			rate, _ = der.Sub(decimal.NewFromFloat(dej)).RoundCeil(2).Float64()
		} else { // 固定值
			der := decimal.NewFromFloat(OKXExchangeRate)
			dej := decimal.NewFromFloat(mch.Floatratedvalue)
			rate, _ = der.Sub(dej).RoundCeil(2).Float64()
		}
	}

	// 金额处理
	finalAmount := req.Amount
	a := decimal.NewFromFloat(finalAmount)
	finalAmount, _ = a.RoundCeil(2).Float64()

	// 最小金额检查 - 根据新项目表结构判断
	// 新项目中没有IsLimitAmount字段，通过LimitAmount是否为空判断
	if mch.LimitAmount != nil && *mch.LimitAmount > 0 && finalAmount < *mch.LimitAmount {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  fmt.Sprintf("最小金额为%.2fU", *mch.LimitAmount),
		})
	}

	// 检查订单是否已存在
	existingOrder, _ := l.svcCtx.Query.PlaceOrder.Where(l.svcCtx.Query.PlaceOrder.MerchantOrderID.Eq(req.MerchantOrderId)).First()
	if existingOrder != nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "订单已存在",
		})
	}

	// 生成订单ID
	tm := time.Now()
	mathrand.Seed(time.Now().UnixNano())
	randomNumber := mathrand.Intn(9000) + 1000
	var orderType string
	var orderID string

	if chain == "Erc" {
		orderType = "Erc20"
		orderID = "PE" + tm.Format("060102150405") + cast.ToString(randomNumber)
	} else if chain == "Trc" {
		orderType = "Trc20"
		orderID = "PT" + tm.Format("060102150405") + cast.ToString(randomNumber)
	} else if chain == "Bsc" {
		orderType = "Bep20"
		orderID = "PB" + tm.Format("060102150405") + cast.ToString(randomNumber)
	}

	// 币种设置
	coin := "USDT"

	// 开始数据库事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		logx.Error("开始事务失败: %v", tx.Error)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 500,
			"msg":  "系统错误",
		})
	}

	// 确保事务在函数结束时处理
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logx.Error("事务panic回滚: %v", r)
		}
	}()

	// 创建下发订单记录
	zeroStatus := int32(0)
	zeroCallbackNum := int32(0)
	placeOrder := &entity.PlaceOrder{
		MerchantsID:     req.MerchantsID,
		OrderID:         orderID,
		MerchantOrderID: req.MerchantOrderId,
		Address:         req.Address,
		Amount:          finalAmount,
		Coin:            coin,
		Status:          &zeroStatus,
		CallbackStatus:  0,
		CallbackURL:     &req.CallbackURL,
		CallbackNum:     &zeroCallbackNum,
		Rate:            &rate,
		ChainName:       &orderType,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	err = tx.Create(placeOrder).Error
	if err != nil {
		tx.Rollback()
		logx.Error("创建下发订单失败: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "创建订单失败",
		})
	}

	// 更新商户链余额 - 使用新的表结构
	newBalance := chainBalance.Balance - totalAmount
	newLockBalance := chainBalance.LockBalance + totalAmount

	err = tx.Model(&entity.MerchantsChainBalance{}).Where(
		"merchants_id = ? AND chain_name = ?", req.MerchantsID, chainNameInDB,
	).Updates(map[string]interface{}{
		"balance":      newBalance,
		"lock_balance": newLockBalance,
		"last_updated": time.Now(),
	}).Error

	if err != nil {
		tx.Rollback()
		logx.Error("更新商户余额失败: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "更新余额失败",
		})
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		logx.Error("提交事务失败: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "提交事务失败",
		})
	}

	// TODO: 订单插入转账队列
	// queue.AddTaskToQueue(orderID)

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "下发下单成功",
		"data": map[string]string{
			"address":         req.Address,
			"orderId":         orderID,
			"merchantOrderId": req.MerchantOrderId,
			"amount":          cast.ToString(finalAmount),
		},
	})
}

// IssuedPayQuery  下发下单查询
func (l *OrderPayLogic) IssuedPayQuery(req *types.IssuedPayQueryRequest) error {
	// 商户验证
	mch, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(req.MerchantsID)).First()
	if err != nil || mch == nil {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户不存在",
		})
	}

	// 商户状态检查
	if mch.Status == 1 {
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 201,
			"msg":  "商户账号已冻结",
		})
	}

	// 查询下发订单（使用OrderID和MerchantsID）
	order, err := l.svcCtx.Query.PlaceOrder.Where(
		l.svcCtx.Query.PlaceOrder.OrderID.Eq(req.OrderID),
		l.svcCtx.Query.PlaceOrder.MerchantsID.Eq(req.MerchantsID),
	).First()

	if err != nil {
		logx.Error("查询下发订单失败: %v", err)
		return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": 404,
			"msg":  "订单不存在",
		})
	}

	// 处理结束时间 - 根据订单状态确定
	var endTime interface{}
	if order.Status != nil && (*order.Status == 1 || *order.Status == 2 || *order.Status == 3 || *order.Status == 4) {
		// 订单已完成、成功、超时或失败时，使用更新时间
		endTime = order.UpdatedAt.Unix()
	} else {
		endTime = nil
	}

	// 构造返回数据 - 保持与老项目字段名一致
	data := map[string]interface{}{
		"address":         order.Address,          // 收款地址
		"orderId":         order.OrderID,          // 系统订单号
		"merchantOrderId": order.MerchantOrderID,  // 商户订单号
		"status":          order.Status,           // 状态描述
		"createdAt":       order.CreatedAt.Unix(), // 创建时间戳
		"endAt":           endTime,                // 结束时间戳
	}

	return l.ctx.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 200,
		"msg":  "查询成功",
		"data": data,
	})
}

func (l *OrderPayLogic) GetTrueAddr(tx *gorm.DB, merchantID int64, money string, chain string) (string, string, int64, error) {
	// 1. 查询被锁定的地址ID列表
	var lockAddr []entity.LockAddress
	err := tx.Where("islock = ?", true).Find(&lockAddr).Error
	if err != nil {
		return "", "", 0, fmt.Errorf("查询锁定地址失败: %v", err)
	}

	lockedAddressIDs := make([]int64, len(lockAddr))
	for i, ls := range lockAddr {
		lockedAddressIDs[i] = ls.AddressID
	}

	// 2. 查询商户信息
	merchants := &entity.Merchants{}
	err = tx.Where("id = ?", merchantID).First(merchants).Error
	if err != nil {
		return "", "", 0, fmt.Errorf("查询商户信息失败: %v", err)
	}

	// 3. 查找可用的地址核心记录
	addressCore := &entity.AddressCore{}
	query := tx.Where("merchants_id = ? AND status = ?", merchantID, 0)

	// 排除被锁定的地址
	if len(lockedAddressIDs) > 0 {
		query = query.Where("id NOT IN ?", lockedAddressIDs)
	}

	err = query.Order("id").First(addressCore).Error

	// 4. 如果没有可用地址，创建新地址
	if err != nil || addressCore == nil {
		logx.Info("没有找到可用地址，创建新地址。merchantID: %d", merchantID)
		return l.createNewAddressLikeOriginal(tx, merchantID, merchants)
	}

	// 5. 获取对应的链地址
	trcAddr, ercAddr, err := l.getChainAddresses(tx, addressCore.ID)
	if err != nil {
		logx.Error("获取链地址失败: %v", err)
		return "", "", 0, err
	}

	// 6. 最终检查地址是否被锁定
	var lockCount int64
	err = tx.Model(&entity.LockAddress{}).Where("islock = ? AND address_id = ?", true, addressCore.ID).Count(&lockCount).Error
	if err != nil {
		return "", "", 0, fmt.Errorf("检查地址锁定状态失败: %v", err)
	}

	// 如果没有锁定记录，说明地址可用
	if lockCount == 0 {
		logx.Info("找到可用地址，addressID: %d, TRC: %s, ERC: %s", addressCore.ID, trcAddr, ercAddr)
		return trcAddr, ercAddr, addressCore.ID, nil
	}

	// 7. 如果被锁定，递归查找下一个地址
	logx.Info("地址被锁定，递归查找下一个地址。addressID: %d", addressCore.ID)
	return l.GetTrueAddr(tx, merchantID, money, chain)
}

// createNewAddressLikeOriginal 完全按照原项目逻辑创建新地址
func (l *OrderPayLogic) createNewAddressLikeOriginal(tx *gorm.DB, merchantID int64, merchants *entity.Merchants) (string, string, int64, error) {
	// 1. 生成新地址 (对应原项目: trc, erc, key, index := CreateAddr())
	trcAddr, ercAddr, privateKey, err := l.CreateAddrWithConfig()
	if err != nil {
		return "", "", 0, err
	}
	// 2. 创建地址核心记录 (对应原项目中的部分字段)
	falseVal := false
	zeroInt32 := int32(0)
	zeroInt64 := int64(0)

	addressCore := &entity.AddressCore{
		PrivateKey:  privateKey,
		MerchantsID: &merchants.ID,
		Lock:        &falseVal,  // 对应原项目的Lock字段
		Locktime:    &zeroInt64, // 对应原项目的锁定时间
		Status:      &zeroInt32, // 对应原项目的DelFlg
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = tx.Create(addressCore).Error
	if err != nil {
		return "", "", 0, fmt.Errorf("创建地址核心记录失败: %v", err)
	}

	// 获取数据库生成的真实ID
	realAddressCoreID := addressCore.ID
	logx.Info("创建地址核心记录成功，真实ID: %d", realAddressCoreID)

	// 3. 创建TRC链地址记录 (对应原项目Address表中的Trxaddr)
	trcAsset := &entity.AddressChain{
		AddressCoreID:    realAddressCoreID, // 使用数据库生成的真实ID
		ChainName:        "TRC",
		Address:          trcAddr,
		NativeBalance:    0,     // 对应原项目的Trx字段
		UsdtBalance:      0,     // 对应原项目的UsdtTrc字段
		TransactionsNums: 0,     // 对应原项目的Num字段
		LockStatus:       false, // 对应原项目的Trxlock字段
		Status:           0,     // 对应原项目的TrxStatus字段
		MerchantsID:      &merchants.ID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = tx.Create(trcAsset).Error
	if err != nil {
		return "", "", 0, fmt.Errorf("创建TRC地址记录失败: %v", err)
	}

	// 4. 创建ERC链地址记录 (对应原项目Address表中的Ethaddr)
	ercAsset := &entity.AddressChain{
		AddressCoreID:    realAddressCoreID, // 使用数据库生成的真实ID
		ChainName:        "ERC",
		Address:          ercAddr,
		NativeBalance:    0,     // 对应原项目的Eth字段
		UsdtBalance:      0,     // 对应原项目的UsdtErc字段
		TransactionsNums: 0,     // 对应原项目的Ercnum字段
		LockStatus:       false, // 对应原项目的Ethlock字段
		Status:           0,     // 对应原项目的EthStatus字段
		MerchantsID:      &merchants.ID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = tx.Create(ercAsset).Error
	if err != nil {
		return "", "", 0, fmt.Errorf("创建ERC地址记录失败: %v", err)
	}

	// 5. 发送WebSocket消息通知 (事务外执行，避免影响事务)
	go func() {
		wsdatatrc := make(map[string]string)
		wsdatatrc["type"] = "addtrc"
		wsdatatrc["address"] = trcAddr
		jsonDataTrc, _ := json.Marshal(wsdatatrc)
		SendMessage(string(jsonDataTrc))

		wsdataerc := make(map[string]string)
		wsdataerc["type"] = "adderc"
		wsdataerc["address"] = ercAddr
		jsonDataErc, _ := json.Marshal(wsdataerc)
		SendMessage(string(jsonDataErc))
	}()

	logx.Info("创建新地址成功，addressID: %d, TRC: %s, ERC: %s", realAddressCoreID, trcAddr, ercAddr)
	return trcAddr, ercAddr, realAddressCoreID, nil
}

// createNewAddress 创建新地址 (保留原方法作为备用)
func (l *OrderPayLogic) createNewAddress(merchantID int64) (string, string, int64, error) {
	merchants, err := l.svcCtx.Query.Merchants.Where(l.svcCtx.Query.Merchants.ID.Eq(merchantID)).First()
	if err != nil {
		return "", "", 0, fmt.Errorf("查询商户信息失败: %v", err)
	}

	// 生成新地址
	trcAddr, ercAddr, privateKey, err := l.CreateAddrWithConfig()

	if err != nil {
		return "", "", 0, fmt.Errorf("生成新地址失败: %v", err)
	}
	// 创建地址核心记录
	falseVal := false
	zeroInt32 := int32(0)
	zeroInt64 := int64(0)

	addressCore := &entity.AddressCore{
		PrivateKey:  privateKey,
		MerchantsID: &merchants.ID,
		Lock:        &falseVal,
		Locktime:    &zeroInt64,
		Status:      &zeroInt32,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = l.svcCtx.Query.AddressCore.Create(addressCore)
	if err != nil {
		return "", "", 0, fmt.Errorf("创建地址核心记录失败: %v", err)
	}

	// 获取数据库生成的真实ID
	realAddressCoreID := addressCore.ID

	// 创建TRC链地址记录
	trcAsset := &entity.AddressChain{
		AddressCoreID:    realAddressCoreID, // 使用数据库生成的真实ID
		ChainName:        "TRC",
		Address:          trcAddr,
		NativeBalance:    0,
		UsdtBalance:      0,
		TransactionsNums: 0,
		LockStatus:       false,
		Status:           0,
		MerchantsID:      &merchants.ID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = l.svcCtx.Query.AddressChain.Create(trcAsset)
	if err != nil {
		return "", "", 0, fmt.Errorf("创建TRC地址记录失败: %v", err)
	}

	// 创建ERC链地址记录
	ercAsset := &entity.AddressChain{
		AddressCoreID:    realAddressCoreID, // 使用数据库生成的真实ID
		ChainName:        "ERC",
		Address:          ercAddr,
		NativeBalance:    0,
		UsdtBalance:      0,
		TransactionsNums: 0,
		LockStatus:       false,
		Status:           0,
		MerchantsID:      &merchants.ID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = l.svcCtx.Query.AddressChain.Create(ercAsset)
	if err != nil {
		return "", "", 0, fmt.Errorf("创建ERC地址记录失败: %v", err)
	}

	// 发送WebSocket消息通知
	wsdatatrc := make(map[string]string)
	wsdatatrc["type"] = "addtrc"
	wsdatatrc["address"] = trcAddr
	jsonDataTrc, _ := json.Marshal(wsdatatrc)
	SendMessage(string(jsonDataTrc))

	wsdataerc := make(map[string]string)
	wsdataerc["type"] = "adderc"
	wsdataerc["address"] = ercAddr
	jsonDataErc, _ := json.Marshal(wsdataerc)
	SendMessage(string(jsonDataErc))

	return trcAddr, ercAddr, realAddressCoreID, nil
}

func (l *OrderPayLogic) getChainAddresses(tx *gorm.DB, addressCoreID int64) (trcAddr, ercAddr string, err error) {
	// 查询TRC地址
	trcAsset := &entity.AddressChain{}
	err = tx.Where("address_core_id = ? AND chain_name = ?", addressCoreID, "TRC").First(trcAsset).Error
	if err != nil {
		return "", "", fmt.Errorf("查询TRC地址失败: %v", err)
	}

	// 查询ERC地址
	ercAsset := &entity.AddressChain{}
	err = tx.Where("address_core_id = ? AND chain_name = ?", addressCoreID, "ERC").First(ercAsset).Error
	if err != nil {
		return "", "", fmt.Errorf("查询ERC地址失败: %v", err)
	}

	return trcAsset.Address, ercAsset.Address, nil
}

// DoLockAddr 锁定地址 (参考原项目逻辑)
func (l *OrderPayLogic) DoLockAddr(tx *gorm.DB, addressId int64, money float64, lockTime int64) bool {
	now := time.Now()
	unlockTime := now.Add(time.Duration(lockTime) * time.Second)

	// 创建锁定记录
	lockRecord := &entity.LockAddress{
		AddressID:  addressId,
		Amount:     money,
		LockTime:   now,
		UnlockTime: unlockTime,
		Islock:     true, // true表示已锁定
	}

	err := tx.Create(lockRecord).Error
	if err != nil {
		logx.Error("创建地址锁定记录失败: %v", err)
		return false
	}

	// 更新地址核心表的锁定状态
	lockStatus := true
	locktime := time.Now().Unix()
	err = tx.Model(&entity.AddressCore{}).Where("id = ?", addressId).Updates(map[string]interface{}{
		"lock":     &lockStatus,
		"locktime": &locktime,
	}).Error

	if err != nil {
		logx.Error("更新地址锁定状态失败: %v", err)
		return false
	}

	logx.Info("地址锁定成功，addressId: %d, money: %f, lockTime: %d秒", addressId, money, lockTime)
	return true
}

func SendMessage(message string) {
	// 解析消息内容，确定消息类型
	var msgData map[string]interface{}
	if err := json.Unmarshal([]byte(message), &msgData); err != nil {
		logx.Error("解析WebSocket消息失败", logx.String("error", err.Error()), logx.String("message", message))
		return
	}

	// 根据消息类型确定WebSocket消息类型
	msgType, ok := msgData["type"].(string)
	if !ok {
		logx.Error("WebSocket消息缺少type字段", logx.String("message", message))
		return
	}

	var wsMessageType websocket.MessageType
	var data interface{}

	switch msgType {
	case "addtrc":
		wsMessageType = websocket.MessageTypeAddTRC
		if address, exists := msgData["address"]; exists {
			data = websocket.AddressMessage{
				Address: address.(string),
			}
		}
	case "adderc":
		wsMessageType = websocket.MessageTypeAddERC
		if address, exists := msgData["address"]; exists {
			data = websocket.AddressMessage{
				Address: address.(string),
			}
		}
	default:
		// 其他类型的消息，使用原始数据
		wsMessageType = websocket.MessageTypeSystemNotice
		data = websocket.SystemMessage{
			Title:   "系统通知",
			Content: message,
			Level:   "info",
		}
	}

	// 广播消息到所有WebSocket客户端
	websocket.BroadcastToAll(wsMessageType, data)

	logx.Info("WebSocket消息已发送", logx.String("type", string(wsMessageType)))
}

// CreateAddrWithConfig 使用配置生成地址
func (l *OrderPayLogic) CreateAddrWithConfig() (trcAddr, ercAddr, privateKey string, error error) {
	// 从配置中获取主私钥
	mainPrivateKeyHex := "d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7"
	logx.Info("主私钥: %s", mainPrivateKeyHex)
	//if mainPrivateKeyHex == "" || mainPrivateKeyHex == "your_master_private_key_here" {
	//	// 如果没有配置主私钥，生成一个示例密钥对用于测试
	//	logx.Info("警告：未配置主私钥，使用演示地址生成")
	//	return generateDemoAddress()
	//}

	// 1. 获取下一个地址索引 (对应原项目逻辑)
	index, err := getNextAddressIndex()
	if err != nil {
		logx.Error("获取地址索引失败: %v", err)
		return "", "", "", err
	}

	// 2. 使用原项目的地址生成算法
	trcAddr, ercAddr, privateKey = generateAddressFromMasterKey(mainPrivateKeyHex, uint64(index))
	//id = index

	logx.Info("生成新地址 - Index: %d, TRC: %s, ERC: %s", index, trcAddr, ercAddr)
	return
}

// getNextAddressIndex 获取下一个地址索引 (对应原项目: lastAddr, _ := dal.Address.Last())
// 注意：这里使用一个简单的时间戳作为索引，避免数据库依赖
// 在生产环境中，建议使用原子操作或数据库序列来确保索引的唯一性
func getNextAddressIndex() (int64, error) {
	// 使用时间戳的纳秒部分作为唯一索引
	return time.Now().UnixNano(), nil
}

// generateAddressFromMasterKey 从主密钥生成地址 (完全按照原项目逻辑)
func generateAddressFromMasterKey(mainPrivateKeyHex string, index uint64) (string, string, string) {
	// 1. 解析主私钥
	privateKey, err := crypto.HexToECDSA(mainPrivateKeyHex)
	if err != nil {
		logx.Error("解析主私钥失败: %v", err)
		return "", "", ""
	}

	// 2. 派生新的私钥
	derivedKey := deriveKeyFromMaster(privateKey, index)

	// 3. 生成公钥
	publicKey := derivedKey.PublicKey

	// 4. 生成ETH地址
	ethAddress := crypto.PubkeyToAddress(publicKey)

	// 5. 生成TRON地址
	tronAddress, err := generateTronAddressFromPublicKey(publicKey)
	if err != nil {
		logx.Error("生成TRON地址失败: %v", err)
		return "", "", ""
	}

	// 6. 加密私钥 (对应原项目的加密逻辑)
	encryptedKey := encryptPrivateKeyWithAddresses(derivedKey, tronAddress, ethAddress.String())

	return tronAddress, ethAddress.Hex(), encryptedKey
}

// deriveKeyFromMaster 从主私钥派生新私钥 (对应原项目的 deriveKey 函数)
func deriveKeyFromMaster(privateKey *ecdsa.PrivateKey, index uint64) *ecdsa.PrivateKey {
	curve := privateKey.Curve
	params := curve.Params()
	bigIndex := new(big.Int).SetUint64(index)
	orderBytes := params.N.Bytes()
	indexBytes := make([]byte, 32)
	copy(indexBytes[32-len(bigIndex.Bytes()):], bigIndex.Bytes())

	// 组合数据进行哈希
	data := append(privateKey.D.Bytes(), orderBytes...)
	data = append(data, indexBytes...)
	hash := crypto.Keccak256(data)

	// 创建派生的私钥
	derivedKey := new(ecdsa.PrivateKey)
	derivedKey.PublicKey.Curve = curve
	derivedKey.D = new(big.Int).SetBytes(hash)
	derivedKey.PublicKey.X, derivedKey.PublicKey.Y = curve.ScalarBaseMult(hash)

	return derivedKey
}

// generateTronAddressFromPublicKey 从公钥生成TRON地址 (对应原项目的 generateTronAddress 函数)
func generateTronAddressFromPublicKey(publicKey ecdsa.PublicKey) (string, error) {
	pubBytes := crypto.FromECDSAPub(&publicKey)
	hash := crypto.Keccak256(pubBytes[1:])[12:]
	address := append([]byte{0x41}, hash...)
	checksum := sha256.Sum256(address)
	checksum = sha256.Sum256(checksum[:])
	address = append(address, checksum[:4]...)
	tronAddress := base58EncodeAddress(address)
	if len(tronAddress) == 0 {
		return "", fmt.Errorf("base58编码失败")
	}
	return tronAddress, nil
}

// base58EncodeAddress Base58编码 (对应原项目的 base58Encode 函数)
func base58EncodeAddress(input []byte) string {
	alphabet := []byte("**********************************************************")
	var result []byte

	x := big.NewInt(0).SetBytes(input)
	base := big.NewInt(58)
	zero := big.NewInt(0)
	mod := &big.Int{}

	for x.Cmp(zero) != 0 {
		x.DivMod(x, base, mod)
		result = append(result, alphabet[mod.Int64()])
	}

	// 反转结果
	for i, j := 0, len(result)-1; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}
	return string(result)
}

// encryptPrivateKeyWithAddresses 使用地址信息加密私钥 (对应原项目的加密逻辑)
func encryptPrivateKeyWithAddresses(derivedKey *ecdsa.PrivateKey, tronAddress, ethAddress string) string {
	// 1. 反转地址字符串
	reversedEthAddress := reverseString(ethAddress)
	reversedTronAddress := reverseString(tronAddress)

	// 2. 使用PBKDF2生成加密密钥
	salt := []byte(reversedTronAddress)
	key := pbkdf2.Key([]byte(reversedEthAddress), salt, 4096, 32, sha256.New)

	// 3. 准备要加密的私钥数据
	plainText := []byte(hex.EncodeToString(derivedKey.D.Bytes()))

	// 4. 加密私钥
	cipherText, err := encryptWithChaCha20(plainText, key)
	if err != nil {
		logx.Error("加密私钥失败: %v", err)
		return hex.EncodeToString(derivedKey.D.Bytes()) // 如果加密失败，返回原始私钥
	}

	return hex.EncodeToString(cipherText)
}

// reverseString 反转字符串 (对应原项目的 reverseString 函数)
func reverseString(s string) string {
	r := []rune(s)
	for i, j := 0, len(r)-1; i < j; i, j = i+1, j-1 {
		r[i], r[j] = r[j], r[i]
	}
	return string(r)
}

// encryptWithChaCha20 使用ChaCha20Poly1305加密 (对应原项目的 encrypt 函数)
func encryptWithChaCha20(plainText, key []byte) ([]byte, error) {
	aead, err := chacha20poly1305.NewX(key)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, chacha20poly1305.NonceSizeX)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	cipherText := aead.Seal(nil, nonce, plainText, nil)
	cipherText = append(nonce, cipherText...)
	return cipherText, nil
}

// addChainEyeMonitoring 添加ChainEye地址监控
func (l *OrderPayLogic) addChainEyeMonitoring(address, chainType string) {
	// 检查配置是否正确加载
	if l.svcCtx.Config.ChainEye.ApiUrl == "" {
		logx.Error("ChainEye API URL配置为空，请检查config.yaml中的ChainEye.api_url配置")
		return
	}

	logx.WithFields(logx.String("api_url", l.svcCtx.Config.ChainEye.ApiUrl)).
		WithFields(logx.String("notify_url", l.svcCtx.Config.ChainEye.NotifyUrl)).
		WithFields(logx.String("address", address)).
		WithFields(logx.String("chain_type", chainType)).
		Info("准备调用ChainEye API")

	// ChainEye API请求数据结构
	type ChainEyeRequest struct {
		ChainType string `json:"chainType"`
		Address   string `json:"address"`
		NotifyUrl string `json:"notifyUrl"`
	}

	// 构造请求数据
	requestData := ChainEyeRequest{
		ChainType: chainType, // 转换为小写，如 TRC -> trx
		Address:   address,
		NotifyUrl: l.svcCtx.Config.ChainEye.NotifyUrl,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logx.Error("序列化ChainEye请求数据失败: %v", err)
		return
	}

	// 创建HTTP请求
	req, err := http.NewRequest("PUT", l.svcCtx.Config.ChainEye.ApiUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		logx.Error("创建ChainEye HTTP请求失败: %v", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("User-Agent", "PayAPI/1.0")

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		logx.Error("发送ChainEye请求失败: %v", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Error("读取ChainEye响应失败: %v", err)
		return
	}

	// 记录请求结果
	if resp.StatusCode == 200 {
		logx.WithFields(logx.String("address", address)).
			WithFields(logx.String("chain_type", chainType)).
			WithFields(logx.String("response", string(body))).
			Info("ChainEye地址监控添加成功")
	} else {
		logx.WithFields(logx.String("address", address)).
			WithFields(logx.String("chain_type", chainType)).
			WithFields(logx.Int("status_code", resp.StatusCode)).
			WithFields(logx.String("response", string(body))).
			Error("ChainEye地址监控添加失败")
	}
}
