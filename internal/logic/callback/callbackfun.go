package callback

import (
	"fmt"
	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"payAPI/model/entity"
	"strings"
	"time"
)

type BackFunLogic struct {
	ctx    *fiber.Ctx
	svcCtx *svc.ServiceContext
}

func NewLogic(ctx *fiber.Ctx, svcCtx *svc.ServiceContext) *BackFunLogic {
	return &BackFunLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 定义与JSON结构匹配的结构体
type Data struct {
	// 转账相关字段
	Amount           int64  `json:"amount"`
	AddressName      string `json:"addressName"`
	TxId             string `json:"txId"`
	Time             string `json:"time"`
	Chain            string `json:"chain"`
	TransferType     string `json:"transferType"`
	ReceivingAddress string `json:"receivingAddress"`
	PaymentAddress   string `json:"paymentAddress"`

	// 能量相关字段
	EnergyLimit int64 `json:"energyLimit"`
}

type RequestBody struct {
	MonitoringType string `json:"monitoringType"`
	NotifyUrl      string `json:"notifyUrl"`
	Address        string `json:"address"`
	Data           Data   `json:"data"` // 嵌套结构体
}

// CallBackFun 回调地址
func (l *BackFunLogic) CallBackFun(ctx *fiber.Ctx, svcCtx *svc.ServiceContext) string {
	// 获取原始请求体数据
	rawBody := ctx.Body()
	logx.WithFields(logx.String("raw_body", string(rawBody))).
		WithFields(logx.String("content_type", ctx.Get("Content-Type"))).
		WithFields(logx.String("method", ctx.Method())).
		WithFields(logx.String("url", ctx.OriginalURL())).
		Info("收到ChainEye原始回调数据")

	// 初始化结构体
	var req RequestBody

	// 解析请求体到结构体
	if err := ctx.BodyParser(&req); err != nil {
		logx.WithFields(logx.String("raw_body", string(rawBody))).
			WithFields(logx.String("error", err.Error())).
			Error("解析请求体失败")
		return "error"
	}

	// 打印解析后的结构化数据
	logx.WithFields(logx.String("monitoringType", req.MonitoringType)).
		WithFields(logx.String("notifyUrl", req.NotifyUrl)).
		WithFields(logx.String("address", req.Address)).
		Info("解析后的基础信息")

	logx.WithFields(logx.String("txId", req.Data.TxId)).
		WithFields(logx.String("chain", req.Data.Chain)).
		WithFields(logx.String("transferType", req.Data.TransferType)).
		WithFields(logx.String("addressName", req.Data.AddressName)).
		WithFields(logx.Int64("amount", req.Data.Amount)).
		WithFields(logx.String("time", req.Data.Time)).
		WithFields(logx.String("receivingAddress", req.Data.ReceivingAddress)).
		WithFields(logx.String("paymentAddress", req.Data.PaymentAddress)).
		Info("解析后的交易数据")

	// 根据监控类型进行不同处理
	switch req.MonitoringType {
	case "transfer":
		// 转账类型回调
		return l.handleTransferCallback(&req)
	case "energy":
		// 能量类型回调
		return l.handleEnergyCallback(&req)
	default:
		logx.WithFields(logx.String("monitoringType", req.MonitoringType)).
			Warn("未知的监控类型")
		return "error"
	}
}

// handleTransferCallback 处理转账类型回调
func (l *BackFunLogic) handleTransferCallback(req *RequestBody) string {
	// 验证必要参数
	if req.Address == "" || req.Data.TxId == "" || req.Data.Chain == "" {
		logx.Error("缺少必要参数")
		return "error"
	}

	// 查询地址信息（支持多链地址查询）
	addressAsset, err := l.svcCtx.Query.AddressChain.Where(
		l.svcCtx.Query.AddressChain.TronAddress.Eq(req.Address).
			Or(l.svcCtx.Query.AddressChain.EthereumAddress.Eq(req.Address)).
			Or(l.svcCtx.Query.AddressChain.BscAddress.Eq(req.Address)),
	).First()

	if err != nil {
		logx.Error("查询地址资产失败: %v", err)
		return "error"
	}

	// 处理交易
	if err := l.processTransaction(req, addressAsset); err != nil {
		logx.Error("处理交易失败: %v", err)
		return "error"
	}

	return "ok"
}

// handleEnergyCallback 处理能量类型回调
func (l *BackFunLogic) handleEnergyCallback(req *RequestBody) string {
	// 验证必要参数
	if req.Address == "" || req.Data.EnergyLimit == 0 {
		logx.Error("能量回调缺少必要参数")
		return "error"
	}

	logx.WithFields(logx.String("address", req.Address)).
		WithFields(logx.Int64("energy_limit", req.Data.EnergyLimit)).
		Info("处理能量更新")

	// 查询地址信息（支持多链地址查询）
	addressAsset, err := l.svcCtx.Query.AddressChain.Where(
		l.svcCtx.Query.AddressChain.TronAddress.Eq(req.Address).
			Or(l.svcCtx.Query.AddressChain.EthereumAddress.Eq(req.Address)).
			Or(l.svcCtx.Query.AddressChain.BscAddress.Eq(req.Address)),
	).First()

	if err != nil {
		logx.Error("查询地址资产失败: %v", err)
		return "error"
	}

	// 更新能量余额（直接覆盖，不是累加）
	if err := l.updateEnergyBalance(addressAsset, req.Data.EnergyLimit); err != nil {
		logx.Error("更新能量余额失败: %v", err)
		return "error"
	}

	return "ok"
}

// processTransaction 处理交易逻辑
func (l *BackFunLogic) processTransaction(req *RequestBody, addressAsset *entity.AddressChain) error {
	// 开始数据库事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logx.Error("事务panic回滚: %v", r)
		}
	}()

	// 计算实际金额（转换为浮点数）
	amount := l.convertAmount(req.Data.Amount, req.Data.AddressName)

	logx.WithFields(logx.String("txId", req.Data.TxId)).
		WithFields(logx.String("transferType", req.Data.TransferType)).
		WithFields(logx.Float64("amount", amount)).
		WithFields(logx.String("addressName", req.Data.AddressName)).
		Info("处理交易")

	// 检查交易是否已经处理过
	existingRecord, _ := l.svcCtx.Query.OrderRecords.Where(
		l.svcCtx.Query.OrderRecords.TransactionID.Eq(req.Data.TxId),
	).First()

	if existingRecord != nil {
		logx.WithFields(logx.String("txId", req.Data.TxId)).Info("交易已处理过，跳过")
		tx.Rollback()
		return nil
	}

	// 根据交易类型处理
	switch req.Data.TransferType {
	case "transferInto":
		// 转入交易 - 更新余额并处理订单
		if err := l.handleTransferIn(tx, req, addressAsset, amount); err != nil {
			tx.Rollback()
			return err
		}
	case "transferOut":
		// 转出交易 - 记录交易但不更新余额（因为是我们主动转出）
		if err := l.handleTransferOut(tx, req, addressAsset, amount); err != nil {
			tx.Rollback()
			return err
		}
	default:
		logx.WithFields(logx.String("transferType", req.Data.TransferType)).Warn("未知的交易类型")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// handleTransferIn 处理转入交易
func (l *BackFunLogic) handleTransferIn(tx *gorm.DB, req *RequestBody, addressAsset *entity.AddressChain, amount float64) error {
	// 更新地址余额
	if err := l.updateAddressBalance(tx, addressAsset, req.Data.Chain, req.Data.AddressName, amount, true); err != nil {
		return fmt.Errorf("更新地址余额失败: %v", err)
	}

	// 查找并更新相关订单，获取订单ID用于创建交易记录
	orderID, err := l.updateRelatedOrder(tx, req, addressAsset, amount)
	if err != nil {
		logx.Error("更新订单失败: %v", err)
		// 订单更新失败不影响余额更新，只记录错误
	}

	// 创建交易记录
	if err := l.createTransactionRecord(tx, req, addressAsset, amount, "转入", orderID); err != nil {
		return fmt.Errorf("创建交易记录失败: %v", err)
	}

	return nil
}

// handleTransferOut 处理转出交易
func (l *BackFunLogic) handleTransferOut(tx *gorm.DB, req *RequestBody, addressAsset *entity.AddressChain, amount float64) error {
	// 转出交易只记录，不更新余额（因为余额在转出时已经扣除）
	// 转出交易通常没有关联的订单ID，传递nil
	if err := l.createTransactionRecord(tx, req, addressAsset, amount, "转出", nil); err != nil {
		return fmt.Errorf("创建交易记录失败: %v", err)
	}

	// 更新交易笔数
	err := tx.Model(&entity.AddressChain{}).Where("id = ?", addressAsset.ID).
		Update("transactions_nums", addressAsset.TransactionsNums+1).Error
	if err != nil {
		return fmt.Errorf("更新交易笔数失败: %v", err)
	}

	return nil
}

// updateAddressBalance 更新地址余额
func (l *BackFunLogic) updateAddressBalance(tx *gorm.DB, addressAsset *entity.AddressChain, chainName string, tokenName string, amount float64, isIncoming bool) error {
	updates := make(map[string]interface{})

	logx.WithFields(logx.String("chain_name", chainName)).
		WithFields(logx.String("token_name", tokenName)).
		WithFields(logx.Float64("amount", amount)).
		WithFields(logx.Bool("is_incoming", isIncoming)).
		Info("开始更新地址余额")

	// 根据链类型和代币类型决定更新哪个余额字段
	switch strings.ToUpper(chainName) {
	case "TRC20":
		// TRC20链上的代币，通常是USDT，更新usdt_balance
		newBalance := addressAsset.UsdtBalance
		if isIncoming {
			newBalance += amount
		} else {
			newBalance -= amount
		}
		updates["usdt_balance"] = newBalance
		logx.WithFields(logx.Float64("new_usdt_balance", newBalance)).Info("更新USDT余额")

	case "TRX":
		// TRX原生代币，更新native_balance
		newBalance := addressAsset.NativeBalance
		if isIncoming {
			newBalance += amount
		} else {
			newBalance -= amount
		}
		updates["native_balance"] = newBalance
		logx.WithFields(logx.Float64("new_native_balance", newBalance)).Info("更新TRX余额")

	default:
		// 检查是否是其他支持的链名称（从数据库获取）
		isValidChain, err := l.isValidChainName(tx, strings.ToUpper(chainName))
		if err != nil {
			return fmt.Errorf("检查链名称失败: %v", err)
		}

		if isValidChain {
			// 其他有效链名称，根据代币类型决定
			if strings.ToUpper(tokenName) == "USDT" {
				// USDT代币，更新usdt_balance
				newBalance := addressAsset.UsdtBalance
				if isIncoming {
					newBalance += amount
				} else {
					newBalance -= amount
				}
				updates["usdt_balance"] = newBalance
			} else {
				// 原生代币，更新native_balance
				newBalance := addressAsset.NativeBalance
				if isIncoming {
					newBalance += amount
				} else {
					newBalance -= amount
				}
				updates["native_balance"] = newBalance
			}
		} else {
			return fmt.Errorf("不支持的链类型: %s", chainName)
		}
	}

	// 更新交易笔数
	updates["transactions_nums"] = addressAsset.TransactionsNums + 1
	updates["updated_at"] = time.Now()

	// 执行更新
	err := tx.Model(&entity.AddressChain{}).Where("id = ?", addressAsset.ID).Updates(updates).Error
	if err != nil {
		return err
	}

	logx.WithFields(logx.Int64("address_id", addressAsset.ID)).
		WithFields(logx.String("token", tokenName)).
		WithFields(logx.Float64("amount", amount)).
		WithFields(logx.Bool("is_incoming", isIncoming)).
		Info("地址余额更新成功")

	return nil
}

// updateEnergyBalance 更新能量余额
func (l *BackFunLogic) updateEnergyBalance(addressAsset *entity.AddressChain, energyLimit int64) error {
	// 开始数据库事务
	tx := l.svcCtx.DBEngine.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logx.Error("能量更新事务panic回滚: %v", r)
		}
	}()

	// 更新能量余额和更新时间
	updates := map[string]interface{}{
		"energy_balance":    energyLimit,
		"energy_updated_at": time.Now(),
		"updated_at":        time.Now(),
	}

	err := tx.Model(&entity.AddressChain{}).
		Where("id = ?", addressAsset.ID).
		Updates(updates).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("更新能量余额失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交能量更新事务失败: %v", err)
	}

	logx.WithFields(logx.Int64("address_id", addressAsset.ID)).
		WithFields(logx.String("address", addressAsset.Address)).
		WithFields(logx.Int64("old_energy", addressAsset.EnergyBalance)).
		WithFields(logx.Int64("new_energy", energyLimit)).
		Info("能量余额更新成功")

	return nil
}

// isValidChainName 检查链名称是否在数据库中存在且启用
func (l *BackFunLogic) isValidChainName(tx *gorm.DB, chainName string) (bool, error) {
	var count int64
	err := tx.Model(&entity.Blockchain{}).
		Where("chain_name = ? AND is_active = ?", chainName, true).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// validateChainName 验证链名称是否在数据库中存在
func (l *BackFunLogic) validateChainName(tx *gorm.DB, chainName string) error {
	var count int64
	err := tx.Model(&entity.Blockchain{}).
		Where("chain_name = ? AND is_active = ?", strings.ToUpper(chainName), true).
		Count(&count).Error

	if err != nil {
		return fmt.Errorf("查询链名称失败: %v", err)
	}

	if count == 0 {
		return fmt.Errorf("不支持的链类型: %s", chainName)
	}

	return nil
}

// getOrderChainName 获取订单查找用的链名称
func (l *BackFunLogic) getOrderChainName(callbackChain string) string {
	// 根据回调中的链名称，转换为订单表中存储的格式
	switch strings.ToUpper(callbackChain) {
	case "TRC20":
		// TRC20代币回调，订单表中可能存储为TRC
		return "TRC"
	case "TRX":
		// TRX原生代币回调，订单表中可能存储为TRC
		return "TRC"
	case "ERC20":
		// ERC20代币回调，订单表中可能存储为ERC
		return "ERC"
	case "ETH":
		// ETH原生代币回调，订单表中可能存储为ERC
		return "ERC"
	case "BEP20":
		// BEP20代币回调，订单表中可能存储为BSC
		return "BSC"
	case "BNB":
		// BNB原生代币回调，订单表中可能存储为BSC
		return "BSC"
	default:
		// 其他情况直接返回大写格式
		return strings.ToUpper(callbackChain)
	}
}

// createTransactionRecord 创建交易记录
func (l *BackFunLogic) createTransactionRecord(tx *gorm.DB, req *RequestBody, addressAsset *entity.AddressChain, amount float64, remark string, orderID *string) error {
	// 如果有关联订单，使用订单ID；否则生成一个基于交易哈希的ID
	var recordID string
	if orderID != nil && *orderID != "" {
		recordID = *orderID
	} else {
		// 对于非订单交易（如直接转账），生成一个唯一ID
		recordID = fmt.Sprintf("TX_%s_%d", req.Data.TxId, time.Now().Unix())
	}

	// 创建交易记录
	record := &entity.OrderRecords{
		ID:            recordID,
		MerchantsID:   addressAsset.MerchantsID,
		ChainName:     req.Data.Chain,
		Type:          1, // 1 商户
		Amount:        amount,
		RorderID:      orderID, // 商户订单ID（如果有的话）
		TransactionID: &req.Data.TxId,
		Remark:        &remark,
		Status:        func() *int32 { s := int32(1); return &s }(), // 1 正常
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := tx.Create(record).Error
	if err != nil {
		return err
	}

	logx.WithFields(logx.String("record_id", recordID)).
		WithFields(logx.String("tx_id", req.Data.TxId)).
		WithFields(logx.Float64("amount", amount)).
		Info("交易记录创建成功")

	return nil
}

// updateRelatedOrder 更新相关订单，返回订单ID用于创建交易记录
func (l *BackFunLogic) updateRelatedOrder(tx *gorm.DB, req *RequestBody, addressAsset *entity.AddressChain, amount float64) (*string, error) {
	// 获取订单查找用的链名称（可能需要转换格式）
	orderChainName := l.getOrderChainName(req.Data.Chain)

	logx.WithFields(logx.String("address", req.Address)).
		WithFields(logx.String("callback_chain", req.Data.Chain)).
		WithFields(logx.String("order_chain", orderChainName)).
		WithFields(logx.Float64("amount", amount)).
		Info("开始查找相关订单")

	// 查找未支付的订单
	var orderEntity entity.Orders
	err := tx.Where("address = ? AND status = ? AND chain_name = ?",
		req.Address, 0, orderChainName).First(&orderEntity).Error

	if err != nil {
		// 没有找到相关订单，可能是直接转账，不是订单支付
		logx.WithFields(logx.String("address", req.Address)).
			WithFields(logx.String("chain_name", orderChainName)).
			WithFields(logx.Float64("amount", amount)).
			Info("未找到相关订单")
		return nil, nil
	}

	logx.WithFields(logx.String("order_id", orderEntity.OrderID)).
		WithFields(logx.Float64("order_amount", orderEntity.Amount)).
		WithFields(logx.Float64("received_amount", amount)).
		Info("找到匹配订单")

	// 检查金额是否匹配（允许一定的误差）
	amountDiff := amount - orderEntity.Amount
	if amountDiff < -0.000001 { // 支付金额不足
		logx.WithFields(logx.String("order_id", orderEntity.OrderID)).
			WithFields(logx.Float64("required", orderEntity.Amount)).
			WithFields(logx.Float64("received", amount)).
			Warn("支付金额不足")
		return nil, nil
	}

	// 更新订单状态
	updates := map[string]interface{}{
		"status":         1, // 支付成功
		"transaction_id": req.Data.TxId,
		"pay_address":    req.Data.PaymentAddress,
		"real_amount":    amount,
		"updated_at":     time.Now(),
	}

	err = tx.Model(&entity.Orders{}).Where("id = ?", orderEntity.ID).Updates(updates).Error
	if err != nil {
		return nil, err
	}

	logx.WithFields(logx.String("order_id", orderEntity.OrderID)).
		WithFields(logx.String("tx_id", req.Data.TxId)).
		WithFields(logx.Float64("amount", amount)).
		Info("订单状态更新成功")

	// 触发回调通知（异步处理）
	go l.triggerCallback(&orderEntity, req.Data.TxId)

	// 返回订单ID用于创建交易记录
	return &orderEntity.OrderID, nil
}

// convertAmount 转换金额（从最小单位转换为标准单位）
func (l *BackFunLogic) convertAmount(amount int64, tokenName string) float64 {
	// 记录原始金额用于调试
	logx.WithFields(logx.Int64("raw_amount", amount)).
		WithFields(logx.String("token_name", tokenName)).
		Info("开始转换金额")

	var result float64
	switch strings.ToUpper(tokenName) {
	case "USDT":
		// USDT在TRC20上是6位小数
		result = decimal.NewFromInt(amount).Div(decimal.NewFromInt(1000000)).InexactFloat64()
	case "TRX":
		// TRX是6位小数
		result = decimal.NewFromInt(amount).Div(decimal.NewFromInt(1000000)).InexactFloat64()
	case "ETH":
		// ETH是18位小数
		result = decimal.NewFromInt(amount).Div(decimal.NewFromInt(1000000000000000000)).InexactFloat64()
	case "BNB":
		// BNB是18位小数
		result = decimal.NewFromInt(amount).Div(decimal.NewFromInt(1000000000000000000)).InexactFloat64()
	default:
		// 默认按6位小数处理
		result = decimal.NewFromInt(amount).Div(decimal.NewFromInt(1000000)).InexactFloat64()
	}

	logx.WithFields(logx.Float64("converted_amount", result)).
		Info("金额转换完成")

	return result
}

// triggerCallback 触发回调通知
func (l *BackFunLogic) triggerCallback(order *entity.Orders, txId string) {
	// 这里应该实现回调通知逻辑
	// 可以调用商户的回调URL，通知订单状态变更
	logx.WithFields(logx.String("order_id", order.OrderID)).
		WithFields(logx.String("callback_url", order.CallbackURL)).
		WithFields(logx.String("tx_id", txId)).
		Info("触发回调通知")

	// TODO: 实现HTTP回调请求
	// 1. 构造回调数据
	// 2. 发送HTTP请求到商户回调URL
	// 3. 处理回调结果
	// 4. 更新回调状态和次数
}
