package config

import (
	"github.com/spf13/viper"
	"payAPI/internal/logx"
)

// Config 配置结构体
type Config struct {
	Log             logx.Config     `yaml:"Log"`
	Server          ServerConfig    `yaml:"Server"`
	PostgresSQLConf PostgresSQLConf `yaml:"PostgresSQLConf"`
	Auth            struct {        // JWT 认证需要的密钥和过期时间配置
		AccessSecret string
		AccessExpire int64
	}
	Cashier  Cashier  `yaml:"Cashier"`
	Crypto   Crypto   `yaml:"Crypto"`
	ChainEye ChainEye `yaml:"ChainEye"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `yaml:"Port"`
}

// PostgresSQL 数据库配置
type PostgresSQL struct {
	Host     string `yaml:"Host"`
	Port     int    `yaml:"Port"`
	User     string `yaml:"User"`
	Password string `yaml:"Password"`
	DBName   string `yaml:"DBName"`
	SSLMode  string `yaml:"SSLMode"`
}

// LoadConfig 加载配置文件
func LoadConfig(configFile string) (*Config, error) {
	viper.SetConfigFile(configFile)
	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// PostgresSQLConf config
type PostgresSQLConf struct {
	Host      string `json:"" yaml:"Host"`
	Port      int64  `json:"" yaml:"Port"`
	User      string `json:"" yaml:"User"`
	Password  string `json:"" yaml:"Password"`
	Database  string `json:"" yaml:"Database"`
	CharSet   string `json:"" yaml:"CharSet"`
	TimeZone  string `json:"" yaml:"TimeZone"`
	ParseTime bool   `json:"" yaml:"ParseTime"`
	Enable    bool   `json:"" yaml:"Enable"` // use PostgresSQL or not

	//DefaultStringSize         uint          `json:"" yaml:"DefaultStringSize"`         // string 类型字段的默认长度
	AutoMigrate bool `json:"" yaml:"AutoMigrate"`
	//DisableDatetimePrecision  bool          `json:"" yaml:"DisableDatetimePrecision"`  // 禁用 datetime 精度
	//SkipInitializeWithVersion bool          `json:"" yaml:"SkipInitializeWithVersion"` // 根据当前 PostgresSQL 版本自动配置
	//
	//SlowSql                   time.Duration `json:"" yaml:"SlowSql"`                   //慢SQL
	//LogLevel                  string        `json:"" yaml:"LogLevel"`                  // 日志记录级别
	//IgnoreRecordNotFoundError bool          `json:"" yaml:"IgnoreRecordNotFoundError"` // 是否忽略ErrRecordNotFound(未查到记录错误)

	Gorm GormConf `json:"" yaml:"Gorm"`
}

// GormConf gorm config
type GormConf struct {
	//SkipDefaultTx   bool   `json:"" yaml:"SkipDefaultTx"`                            //是否跳过默认事务
	//CoverLogger     bool   `json:"" yaml:"CoverLogger"`                              //是否覆盖默认logger
	//PreparedStmt    bool   `json:"" yaml:"PreparedStmt"`                              // 设置SQL缓存
	//CloseForeignKey bool   `json:"" yaml:"CloseForeignKey"` 						// 禁用外键约束
	SingularTable   bool   `json:"" yaml:"SingularTable"` //是否使用单数表名(默认复数)，启用后，User结构体表将是user
	TablePrefix     string `json:"" yaml:"TablePrefix"`   // 表前缀
	MaxOpenConns    int    `json:"" yaml:"MaxOpenConns"`
	MaxIdleConns    int    `json:"" yaml:"MaxIdleConns"`
	ConnMaxLifetime int    `json:"" yaml:"ConnMaxLifetime"`
}

type Cashier struct {
	Url string `json:"" yaml:"url"`
}

// Crypto 加密相关配置
type Crypto struct {
	MasterPrivateKey string `json:"" yaml:"master_private_key"` // 主私钥，用于生成地址
}

// ChainEye 链眼配置
type ChainEye struct {
	ApiUrl    string // ChainEye API地址
	NotifyUrl string // 回调通知地址
}
