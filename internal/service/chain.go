package service

import (
	"errors"
	"fmt"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"strings"
)

// ChainService 链管理服务
type ChainService struct {
	svcCtx *svc.ServiceContext
}

// NewChainService 创建链管理服务实例
func NewChainService(svcCtx *svc.ServiceContext) *ChainService {
	return &ChainService{
		svcCtx: svcCtx,
	}
}

// ChainConfig 链配置信息
type ChainConfig struct {
	ChainName     string  `json:"chain_name"`     // 链名称
	DisplayName   string  `json:"display_name"`   // 显示名称
	NativeToken   string  `json:"native_token"`   // 原生代币名称
	USDTContract  string  `json:"usdt_contract"`  // USDT合约地址
	Decimals      int32   `json:"decimals"`       // 精度
	IsActive      bool    `json:"is_active"`      // 是否启用
	SortOrder     int32   `json:"sort_order"`     // 排序
	Description   string  `json:"description"`    // 描述
	ExplorerURL   string  `json:"explorer_url"`   // 区块链浏览器URL
	RPCEndpoint   string  `json:"rpc_endpoint"`   // RPC端点
	WebsocketURL  string  `json:"websocket_url"`  // WebSocket URL
	MinAmount     float64 `json:"min_amount"`     // 最小金额
	MaxAmount     float64 `json:"max_amount"`     // 最大金额
	ConfirmBlocks int32   `json:"confirm_blocks"` // 确认块数
}

// 预定义的链配置
var predefinedChains = map[string]*ChainConfig{
	"TRC20": {
		ChainName:     "TRC20",
		DisplayName:   "TRON (TRC20)",
		NativeToken:   "TRX",
		USDTContract:  "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
		Decimals:      6,
		IsActive:      true,
		SortOrder:     1,
		Description:   "TRON网络，低手续费，高TPS",
		ExplorerURL:   "https://tronscan.org",
		RPCEndpoint:   "https://api.trongrid.io",
		WebsocketURL:  "wss://api.trongrid.io",
		MinAmount:     1.0,
		MaxAmount:     100000.0,
		ConfirmBlocks: 20,
	},
	"ERC20": {
		ChainName:     "ERC20",
		DisplayName:   "Ethereum (ERC20)",
		NativeToken:   "ETH",
		USDTContract:  "******************************************",
		Decimals:      6,
		IsActive:      true,
		SortOrder:     2,
		Description:   "以太坊网络，安全稳定",
		ExplorerURL:   "https://etherscan.io",
		RPCEndpoint:   "https://mainnet.infura.io",
		WebsocketURL:  "wss://mainnet.infura.io/ws",
		MinAmount:     10.0,
		MaxAmount:     50000.0,
		ConfirmBlocks: 12,
	},
	"BSC20": {
		ChainName:     "BSC20",
		DisplayName:   "Binance Smart Chain (BEP20)",
		NativeToken:   "BNB",
		USDTContract:  "******************************************",
		Decimals:      18,
		IsActive:      false, // 默认禁用
		SortOrder:     3,
		Description:   "币安智能链，快速低费",
		ExplorerURL:   "https://bscscan.com",
		RPCEndpoint:   "https://bsc-dataseed.binance.org",
		WebsocketURL:  "wss://bsc-ws-node.nariox.org:443/ws",
		MinAmount:     5.0,
		MaxAmount:     80000.0,
		ConfirmBlocks: 15,
	},
	"POLYGON": {
		ChainName:     "POLYGON",
		DisplayName:   "Polygon (MATIC)",
		NativeToken:   "MATIC",
		USDTContract:  "******************************************",
		Decimals:      6,
		IsActive:      false, // 默认禁用
		SortOrder:     4,
		Description:   "Polygon网络，以太坊Layer2解决方案",
		ExplorerURL:   "https://polygonscan.com",
		RPCEndpoint:   "https://polygon-rpc.com",
		WebsocketURL:  "wss://polygon-rpc.com/ws",
		MinAmount:     1.0,
		MaxAmount:     100000.0,
		ConfirmBlocks: 30,
	},
}

// GetAllChainConfigs 获取所有链配置
func (cs *ChainService) GetAllChainConfigs() map[string]*ChainConfig {
	return predefinedChains
}

// GetChainConfig 获取指定链的配置
func (cs *ChainService) GetChainConfig(chainName string) (*ChainConfig, error) {
	config, exists := predefinedChains[chainName]
	if !exists {
		return nil, errors.New("链配置不存在")
	}
	
	// 返回配置的副本，避免外部修改
	configCopy := *config
	return &configCopy, nil
}

// GetActiveChains 获取所有启用的链
func (cs *ChainService) GetActiveChains() []*ChainConfig {
	var activeChains []*ChainConfig
	
	for _, config := range predefinedChains {
		if config.IsActive {
			// 返回配置的副本
			configCopy := *config
			activeChains = append(activeChains, &configCopy)
		}
	}
	
	return activeChains
}

// GetActiveChainNames 获取所有启用的链名称
func (cs *ChainService) GetActiveChainNames() []string {
	var chainNames []string
	
	for chainName, config := range predefinedChains {
		if config.IsActive {
			chainNames = append(chainNames, chainName)
		}
	}
	
	return chainNames
}

// EnableChain 启用链
func (cs *ChainService) EnableChain(chainName string) error {
	chainName = strings.ToUpper(chainName)
	config, exists := predefinedChains[chainName]
	if !exists {
		return errors.New("链配置不存在")
	}
	
	if config.IsActive {
		return errors.New("链已经处于启用状态")
	}
	
	config.IsActive = true
	
	logx.Info("链已启用", 
		logx.String("chain_name", chainName),
		logx.String("display_name", config.DisplayName))
	
	return nil
}

// DisableChain 禁用链
func (cs *ChainService) DisableChain(chainName string) error {
	chainName = strings.ToUpper(chainName)
	config, exists := predefinedChains[chainName]
	if !exists {
		return errors.New("链配置不存在")
	}
	
	if !config.IsActive {
		return errors.New("链已经处于禁用状态")
	}
	
	// 检查是否有活跃的余额或订单
	hasActiveBalance, err := cs.checkActiveBalance(chainName)
	if err != nil {
		return err
	}
	
	if hasActiveBalance {
		return errors.New("该链存在活跃余额或订单，无法禁用")
	}
	
	config.IsActive = false
	
	logx.Info("链已禁用", 
		logx.String("chain_name", chainName),
		logx.String("display_name", config.DisplayName))
	
	return nil
}

// IsChainActive 检查链是否启用
func (cs *ChainService) IsChainActive(chainName string) bool {
	chainName = strings.ToUpper(chainName)
	config, exists := predefinedChains[chainName]
	if !exists {
		return false
	}
	
	return config.IsActive
}

// ValidateChainName 验证链名称是否有效
func (cs *ChainService) ValidateChainName(chainName string) error {
	chainName = strings.ToUpper(chainName)
	_, exists := predefinedChains[chainName]
	if !exists {
		return errors.New("无效的链名称")
	}
	
	return nil
}

// GetChainLimits 获取链的金额限制
func (cs *ChainService) GetChainLimits(chainName string) (minAmount, maxAmount float64, err error) {
	config, err := cs.GetChainConfig(chainName)
	if err != nil {
		return 0, 0, err
	}
	
	return config.MinAmount, config.MaxAmount, nil
}

// ValidateAmount 验证金额是否在链的限制范围内
func (cs *ChainService) ValidateAmount(chainName string, amount float64) error {
	minAmount, maxAmount, err := cs.GetChainLimits(chainName)
	if err != nil {
		return err
	}
	
	if amount < minAmount {
		return errors.New(fmt.Sprintf("金额不能小于 %.2f", minAmount))
	}
	
	if amount > maxAmount {
		return errors.New(fmt.Sprintf("金额不能大于 %.2f", maxAmount))
	}
	
	return nil
}

// GetSupportedChains 获取所有支持的链（包括未启用的）
func (cs *ChainService) GetSupportedChains() []*ChainConfig {
	var allChains []*ChainConfig
	
	for _, config := range predefinedChains {
		// 返回配置的副本
		configCopy := *config
		allChains = append(allChains, &configCopy)
	}
	
	return allChains
}

// UpdateChainConfig 更新链配置（仅限某些字段）
func (cs *ChainService) UpdateChainConfig(chainName string, updates map[string]interface{}) error {
	chainName = strings.ToUpper(chainName)
	config, exists := predefinedChains[chainName]
	if !exists {
		return errors.New("链配置不存在")
	}
	
	// 只允许更新特定字段
	allowedFields := map[string]bool{
		"display_name":  true,
		"description":   true,
		"min_amount":    true,
		"max_amount":    true,
		"rpc_endpoint":  true,
		"websocket_url": true,
		"sort_order":    true,
	}
	
	for field, value := range updates {
		if !allowedFields[field] {
			return errors.New(fmt.Sprintf("不允许修改字段: %s", field))
		}
		
		switch field {
		case "display_name":
			if str, ok := value.(string); ok {
				config.DisplayName = str
			}
		case "description":
			if str, ok := value.(string); ok {
				config.Description = str
			}
		case "min_amount":
			if amount, ok := value.(float64); ok {
				config.MinAmount = amount
			}
		case "max_amount":
			if amount, ok := value.(float64); ok {
				config.MaxAmount = amount
			}
		case "rpc_endpoint":
			if str, ok := value.(string); ok {
				config.RPCEndpoint = str
			}
		case "websocket_url":
			if str, ok := value.(string); ok {
				config.WebsocketURL = str
			}
		case "sort_order":
			if order, ok := value.(int32); ok {
				config.SortOrder = order
			}
		}
	}
	
	logx.Info("链配置已更新", 
		logx.String("chain_name", chainName),
		logx.Any("updates", updates))
	
	return nil
}

// checkActiveBalance 检查链是否有活跃余额（内部方法）
func (cs *ChainService) checkActiveBalance(chainName string) (bool, error) {
	// 检查是否有商户在该链上有余额
	count, err := cs.svcCtx.Query.MerchantsChainBalance.Where(
		cs.svcCtx.Query.MerchantsChainBalance.ChainName.Eq(chainName),
		cs.svcCtx.Query.MerchantsChainBalance.Balance.Gt(0),
	).Count()
	
	if err != nil {
		return false, err
	}
	
	if count > 0 {
		return true, nil
	}
	
	// 检查是否有该链的活跃订单
	orderCount, err := cs.svcCtx.Query.Orders.Where(
		cs.svcCtx.Query.Orders.ChainName.Eq(chainName),
		cs.svcCtx.Query.Orders.Status.Eq(0), // 未支付状态
	).Count()
	
	if err != nil {
		return false, err
	}
	
	return orderCount > 0, nil
}