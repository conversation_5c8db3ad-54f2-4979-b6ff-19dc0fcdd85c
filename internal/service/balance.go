package service

import (
	"errors"
	"payAPI/dal"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"payAPI/model/entity"
	"time"

	"github.com/shopspring/decimal"
)

// BalanceService 余额管理服务
type BalanceService struct {
	svcCtx *svc.ServiceContext
}

// NewBalanceService 创建余额服务实例
func NewBalanceService(svcCtx *svc.ServiceContext) *BalanceService {
	return &BalanceService{
		svcCtx: svcCtx,
	}
}

// BalanceInfo 余额信息
type BalanceInfo struct {
	MerchantsID  int64   `json:"merchants_id"`  // 商户ID
	ChainName    string  `json:"chain_name"`    // 链名称
	Balance      float64 `json:"balance"`       // 可用余额
	LockBalance  float64 `json:"lock_balance"`  // 锁定余额
	FeeBalance   float64 `json:"fee_balance"`   // 手续费余额
	TotalBalance float64 `json:"total_balance"` // 总余额
}

// GetMerchantBalance 获取商户在指定链的余额信息
func (bs *BalanceService) GetMerchantBalance(merchantsID int64, chainName string) (*BalanceInfo, error) {
	balance, err := bs.svcCtx.Query.MerchantsChainBalance.Where(
		bs.svcCtx.Query.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
		bs.svcCtx.Query.MerchantsChainBalance.ChainName.Eq(chainName),
	).First()
	logx.Info("GetMerchantBalance: %v", balance)
	logx.Info("GetMerchantBalance: %v", chainName)
	logx.Info("GetMerchantBalance: %v", merchantsID)
	if err != nil {
		// 如果余额记录不存在，创建默认记录
		if errors.Is(err, errors.New("record not found")) {
			return bs.createDefaultBalance(merchantsID, chainName)
		}
		return nil, err
	}

	totalBalance := balance.Balance + balance.LockBalance

	return &BalanceInfo{
		MerchantsID:  balance.MerchantsID,
		ChainName:    balance.ChainName,
		Balance:      balance.Balance,
		LockBalance:  balance.LockBalance,
		FeeBalance:   balance.FeeBalance,
		TotalBalance: totalBalance,
	}, nil
}

// GetAllMerchantBalances 获取商户所有链的余额信息
func (bs *BalanceService) GetAllMerchantBalances(merchantsID int64) ([]*BalanceInfo, error) {
	balances, err := bs.svcCtx.Query.MerchantsChainBalance.Where(
		bs.svcCtx.Query.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
	).Find()

	if err != nil {
		return nil, err
	}

	var balanceInfos []*BalanceInfo
	for _, balance := range balances {
		totalBalance := balance.Balance + balance.LockBalance

		balanceInfos = append(balanceInfos, &BalanceInfo{
			MerchantsID:  balance.MerchantsID,
			ChainName:    balance.ChainName,
			Balance:      balance.Balance,
			LockBalance:  balance.LockBalance,
			FeeBalance:   balance.FeeBalance,
			TotalBalance: totalBalance,
		})
	}

	return balanceInfos, nil
}

// CheckSufficientBalance 检查余额是否充足
func (bs *BalanceService) CheckSufficientBalance(merchantsID int64, chainName string, amount float64) (bool, error) {
	balance, err := bs.GetMerchantBalance(merchantsID, chainName)
	if err != nil {
		return false, err
	}

	return balance.Balance >= amount, nil
}

// CheckSufficientFeeBalance 检查手续费余额是否充足
func (bs *BalanceService) CheckSufficientFeeBalance(merchantsID int64, chainName string, feeAmount float64) (bool, error) {
	balance, err := bs.GetMerchantBalance(merchantsID, chainName)
	if err != nil {
		return false, err
	}

	return balance.FeeBalance >= feeAmount, nil
}

// LockBalance 锁定余额
func (bs *BalanceService) LockBalance(merchantsID int64, chainName string, amount float64) error {
	// 使用高精度计算
	lockAmount := decimal.NewFromFloat(amount)

	// 检查余额是否充足
	sufficient, err := bs.CheckSufficientBalance(merchantsID, chainName, amount)
	if err != nil {
		return err
	}

	if !sufficient {
		return errors.New("余额不足")
	}

	// 原子性更新余额和锁定余额
	err = bs.svcCtx.Query.Transaction(func(tx *dal.Query) error {
		balance, err := tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
			tx.MerchantsChainBalance.ChainName.Eq(chainName),
		).First()

		if err != nil {
			return err
		}

		// 计算新的余额
		newBalance, _ := decimal.NewFromFloat(balance.Balance).Sub(lockAmount).Float64()
		newLockBalance, _ := decimal.NewFromFloat(balance.LockBalance).Add(lockAmount).Float64()

		// 更新余额
		_, err = tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.ID.Eq(balance.ID),
		).Updates(&entity.MerchantsChainBalance{
			Balance:     newBalance,
			LockBalance: newLockBalance,
			LastUpdated: time.Now(),
		})

		return err
	})

	if err != nil {
		logx.Error("锁定余额失败",
			logx.Int64("merchants_id", merchantsID),
			logx.String("chain_name", chainName),
			logx.Float64("amount", amount),
			logx.String("error", err.Error()))
		return err
	}

	logx.Info("余额锁定成功",
		logx.Int64("merchants_id", merchantsID),
		logx.String("chain_name", chainName),
		logx.Float64("amount", amount))

	return nil
}

// UnlockBalance 解锁余额
func (bs *BalanceService) UnlockBalance(merchantsID int64, chainName string, amount float64) error {
	// 使用高精度计算
	unlockAmount := decimal.NewFromFloat(amount)

	// 原子性更新余额和锁定余额
	err := bs.svcCtx.Query.Transaction(func(tx *dal.Query) error {
		balance, err := tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
			tx.MerchantsChainBalance.ChainName.Eq(chainName),
		).First()

		if err != nil {
			return err
		}

		// 检查锁定余额是否充足
		if balance.LockBalance < amount {
			return errors.New("锁定余额不足")
		}

		// 计算新的余额
		newBalance, _ := decimal.NewFromFloat(balance.Balance).Add(unlockAmount).Float64()
		newLockBalance, _ := decimal.NewFromFloat(balance.LockBalance).Sub(unlockAmount).Float64()

		// 更新余额
		_, err = tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.ID.Eq(balance.ID),
		).Updates(&entity.MerchantsChainBalance{
			Balance:     newBalance,
			LockBalance: newLockBalance,
			LastUpdated: time.Now(),
		})

		return err
	})

	if err != nil {
		logx.Error("解锁余额失败",
			logx.Int64("merchants_id", merchantsID),
			logx.String("chain_name", chainName),
			logx.Float64("amount", amount),
			logx.String("error", err.Error()))
		return err
	}

	logx.Info("余额解锁成功",
		logx.Int64("merchants_id", merchantsID),
		logx.String("chain_name", chainName),
		logx.Float64("amount", amount))

	return nil
}

// DeductBalance 扣除余额（用于支付订单）
func (bs *BalanceService) DeductBalance(merchantsID int64, chainName string, amount float64) error {
	// 使用高精度计算
	deductAmount := decimal.NewFromFloat(amount)

	// 原子性扣除锁定余额
	err := bs.svcCtx.Query.Transaction(func(tx *dal.Query) error {
		balance, err := tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
			tx.MerchantsChainBalance.ChainName.Eq(chainName),
		).First()

		if err != nil {
			return err
		}

		// 检查锁定余额是否充足
		if balance.LockBalance < amount {
			return errors.New("锁定余额不足")
		}

		// 扣除锁定余额
		newLockBalance, _ := decimal.NewFromFloat(balance.LockBalance).Sub(deductAmount).Float64()

		// 更新余额
		_, err = tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.ID.Eq(balance.ID),
		).Updates(&entity.MerchantsChainBalance{
			LockBalance: newLockBalance,
			LastUpdated: time.Now(),
		})

		return err
	})

	if err != nil {
		logx.Error("扣除余额失败",
			logx.Int64("merchants_id", merchantsID),
			logx.String("chain_name", chainName),
			logx.Float64("amount", amount),
			logx.String("error", err.Error()))
		return err
	}

	logx.Info("余额扣除成功",
		logx.Int64("merchants_id", merchantsID),
		logx.String("chain_name", chainName),
		logx.Float64("amount", amount))

	return nil
}

// createDefaultBalance 创建默认余额记录
func (bs *BalanceService) createDefaultBalance(merchantsID int64, chainName string) (*BalanceInfo, error) {
	defaultBalance := &entity.MerchantsChainBalance{
		MerchantsID:       merchantsID,
		ChainName:         chainName,
		Balance:           0.00000,
		LockBalance:       0.00000,
		FeeBalance:        0.00000,
		GatherStatus:      0,
		NeedGatherBalance: 0.00000,
		LastUpdated:       time.Now(),
	}

	err := bs.svcCtx.Query.MerchantsChainBalance.Create(defaultBalance)
	if err != nil {
		return nil, err
	}

	return &BalanceInfo{
		MerchantsID:  merchantsID,
		ChainName:    chainName,
		Balance:      0.00000,
		LockBalance:  0.00000,
		FeeBalance:   0.00000,
		TotalBalance: 0.00000,
	}, nil
}
