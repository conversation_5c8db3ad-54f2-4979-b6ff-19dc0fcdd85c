package service

import (
	"fmt"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"time"
)

// StatisticsService 统计分析服务
type StatisticsService struct {
	svcCtx *svc.ServiceContext
}

// NewStatisticsService 创建统计服务实例
func NewStatisticsService(svcCtx *svc.ServiceContext) *StatisticsService {
	return &StatisticsService{
		svcCtx: svcCtx,
	}
}

// ChainStatistics 链统计信息
type ChainStatistics struct {
	ChainName          string  `json:"chain_name"`           // 链名称
	TotalAddresses     int64   `json:"total_addresses"`      // 总地址数
	ActiveAddresses    int64   `json:"active_addresses"`     // 活跃地址数
	LockedAddresses    int64   `json:"locked_addresses"`     // 锁定地址数
	TotalNativeBalance float64 `json:"total_native_balance"` // 总主币余额
	TotalUsdtBalance   float64 `json:"total_usdt_balance"`   // 总USDT余额
	TotalTransactions  int64   `json:"total_transactions"`   // 总交易数
	AvgBalance         float64 `json:"avg_balance"`          // 平均余额
}

// MerchantStatistics 商户统计信息
type MerchantStatistics struct {
	MerchantsID      int64                       `json:"merchants_id"`       // 商户ID
	TotalBalance     float64                     `json:"total_balance"`      // 总余额
	TotalLockBalance float64                     `json:"total_lock_balance"` // 总锁定余额
	TotalFeeBalance  float64                     `json:"total_fee_balance"`  // 总手续费余额
	ChainBalances    map[string]*BalanceInfo     `json:"chain_balances"`     // 各链余额
	ChainStats       map[string]*ChainStatistics `json:"chain_stats"`        // 各链统计
}

// SystemStatistics 系统整体统计
type SystemStatistics struct {
	TotalMerchants   int64                       `json:"total_merchants"`   // 总商户数
	TotalAddresses   int64                       `json:"total_addresses"`   // 总地址数
	TotalOrders      int64                       `json:"total_orders"`      // 总订单数
	TotalAmount      float64                     `json:"total_amount"`      // 总交易金额
	ActiveChains     []string                    `json:"active_chains"`     // 活跃链列表
	ChainStatistics  map[string]*ChainStatistics `json:"chain_statistics"`  // 各链详细统计
	OrderStatistics  *OrderStatistics            `json:"order_statistics"`  // 订单统计
	RecentActivities []*RecentActivity           `json:"recent_activities"` // 最近活动
}

// OrderStatistics 订单统计
type OrderStatistics struct {
	TotalOrders     int64   `json:"total_orders"`     // 总订单数
	PendingOrders   int64   `json:"pending_orders"`   // 待支付订单
	CompletedOrders int64   `json:"completed_orders"` // 已完成订单
	CanceledOrders  int64   `json:"canceled_orders"`  // 已取消订单
	TotalAmount     float64 `json:"total_amount"`     // 总金额
	AvgOrderAmount  float64 `json:"avg_order_amount"` // 平均订单金额
	TodayOrders     int64   `json:"today_orders"`     // 今日订单数
	TodayAmount     float64 `json:"today_amount"`     // 今日交易金额
}

// RecentActivity 最近活动
type RecentActivity struct {
	Type        string      `json:"type"`        // 活动类型
	Description string      `json:"description"` // 活动描述
	Data        interface{} `json:"data"`        // 活动数据
	Timestamp   int64       `json:"timestamp"`   // 时间戳
}

// GetChainStatistics 获取指定链的统计信息
func (ss *StatisticsService) GetChainStatistics(chainName string) (*ChainStatistics, error) {
	// 查询地址统计
	var totalAddresses int64
	var activeAddresses int64
	var lockedAddresses int64
	var totalNativeBalance float64
	var totalUsdtBalance float64
	var totalTransactions int64

	// 统计地址数量
	addresses, err := ss.svcCtx.Query.AddressChain.Where(
		ss.svcCtx.Query.AddressChain.ChainName.Eq(chainName),
	).Find()

	if err != nil {
		return nil, err
	}

	totalAddresses = int64(len(addresses))

	for _, addr := range addresses {
		// 统计活跃地址（有余额的地址）
		if addr.NativeBalance > 0 || addr.UsdtBalance > 0 {
			activeAddresses++
		}

		// 统计锁定地址
		if addr.LockStatus {
			lockedAddresses++
		}

		// 累计余额
		totalNativeBalance += addr.NativeBalance
		totalUsdtBalance += addr.UsdtBalance
		totalTransactions += int64(addr.TransactionsNums)
	}

	// 计算平均余额
	avgBalance := float64(0)
	if totalAddresses > 0 {
		avgBalance = (totalNativeBalance + totalUsdtBalance) / float64(totalAddresses)
	}

	return &ChainStatistics{
		ChainName:          chainName,
		TotalAddresses:     totalAddresses,
		ActiveAddresses:    activeAddresses,
		LockedAddresses:    lockedAddresses,
		TotalNativeBalance: totalNativeBalance,
		TotalUsdtBalance:   totalUsdtBalance,
		TotalTransactions:  totalTransactions,
		AvgBalance:         avgBalance,
	}, nil
}

// GetMerchantStatistics 获取商户统计信息
func (ss *StatisticsService) GetMerchantStatistics(merchantsID int64) (*MerchantStatistics, error) {
	balanceService := NewBalanceService(ss.svcCtx)

	// 获取商户所有链的余额
	balances, err := balanceService.GetAllMerchantBalances(merchantsID)
	if err != nil {
		return nil, err
	}

	chainBalances := make(map[string]*BalanceInfo)
	chainStats := make(map[string]*ChainStatistics)

	totalBalance := float64(0)
	totalLockBalance := float64(0)
	totalFeeBalance := float64(0)

	for _, balance := range balances {
		chainBalances[balance.ChainName] = balance

		// 累计总余额
		totalBalance += balance.Balance
		totalLockBalance += balance.LockBalance
		totalFeeBalance += balance.FeeBalance

		// 获取该链的统计信息
		chainStat, err := ss.GetChainStatistics(balance.ChainName)
		if err != nil {
			logx.Warn("获取链统计失败",
				logx.String("chain", balance.ChainName),
				logx.String("error", err.Error()))
		} else {
			chainStats[balance.ChainName] = chainStat
		}
	}

	return &MerchantStatistics{
		MerchantsID:      merchantsID,
		TotalBalance:     totalBalance,
		TotalLockBalance: totalLockBalance,
		TotalFeeBalance:  totalFeeBalance,
		ChainBalances:    chainBalances,
		ChainStats:       chainStats,
	}, nil
}

// GetSystemStatistics 获取系统整体统计信息
func (ss *StatisticsService) GetSystemStatistics() (*SystemStatistics, error) {
	// 统计商户数量
	totalMerchants, err := ss.svcCtx.Query.Merchants.Count()
	if err != nil {
		return nil, err
	}

	// 统计地址数量
	totalAddresses, err := ss.svcCtx.Query.AddressCore.Count()
	if err != nil {
		return nil, err
	}

	// 统计订单信息
	orderStats, err := ss.getOrderStatistics()
	if err != nil {
		logx.Warn("获取订单统计失败", logx.String("error", err.Error()))
	}

	// 获取活跃链列表
	activeChains, err := ss.GetActiveChains()
	if err != nil {
		logx.Warn("获取活跃链列表失败", logx.String("error", err.Error()))
		activeChains = []string{"TRC20", "ERC20"} // 默认链
	}

	// 获取各链统计信息
	chainStatistics := make(map[string]*ChainStatistics)
	totalAmount := float64(0)

	for _, chainName := range activeChains {
		chainStat, err := ss.GetChainStatistics(chainName)
		if err != nil {
			logx.Warn("获取链统计失败",
				logx.String("chain", chainName),
				logx.String("error", err.Error()))
		} else {
			chainStatistics[chainName] = chainStat
			totalAmount += chainStat.TotalNativeBalance + chainStat.TotalUsdtBalance
		}
	}

	// 获取最近活动
	recentActivities := ss.getRecentActivities()

	return &SystemStatistics{
		TotalMerchants:   totalMerchants,
		TotalAddresses:   totalAddresses,
		TotalOrders:      orderStats.TotalOrders,
		TotalAmount:      totalAmount,
		ActiveChains:     activeChains,
		ChainStatistics:  chainStatistics,
		OrderStatistics:  orderStats,
		RecentActivities: recentActivities,
	}, nil
}

// GetActiveChains 获取活跃链列表
func (ss *StatisticsService) GetActiveChains() ([]string, error) {
	// 查询所有有地址的链
	var chains []string

	err := ss.svcCtx.DBEngine.Raw(`
		SELECT DISTINCT chain_name 
		FROM address_chain_asset 
		WHERE deleted_at IS NULL
		ORDER BY chain_name
	`).Scan(&chains).Error

	return chains, err
}

// getOrderStatistics 获取订单统计（内部方法）
func (ss *StatisticsService) getOrderStatistics() (*OrderStatistics, error) {
	// 统计总订单数
	totalOrders, err := ss.svcCtx.Query.Orders.Count()
	if err != nil {
		return nil, err
	}

	// 按状态统计订单
	pendingOrders, _ := ss.svcCtx.Query.Orders.Where(
		ss.svcCtx.Query.Orders.Status.Eq(0),
	).Count()

	completedOrders, _ := ss.svcCtx.Query.Orders.Where(
		ss.svcCtx.Query.Orders.Status.Eq(1),
	).Count()

	canceledOrders, _ := ss.svcCtx.Query.Orders.Where(
		ss.svcCtx.Query.Orders.Status.Eq(3),
	).Count()

	// 统计金额
	var totalAmount float64
	var avgOrderAmount float64

	if totalOrders > 0 {
		err = ss.svcCtx.DBEngine.Raw(`
			SELECT COALESCE(SUM(amount), 0) as total_amount, 
			       COALESCE(AVG(amount), 0) as avg_amount 
			FROM orders 
			WHERE deleted_at IS NULL
		`).Row().Scan(&totalAmount, &avgOrderAmount)

		if err != nil {
			logx.Warn("查询订单金额统计失败", logx.String("error", err.Error()))
		}
	}

	// 统计今日订单
	today := time.Now().Format("2006-01-02")
	var todayOrders int64
	var todayAmount float64

	err = ss.svcCtx.DBEngine.Raw(`
		SELECT COUNT(*) as order_count, 
		       COALESCE(SUM(amount), 0) as total_amount 
		FROM orders 
		WHERE DATE(created_at) = ? AND deleted_at IS NULL
	`, today).Row().Scan(&todayOrders, &todayAmount)

	if err != nil {
		logx.Warn("查询今日订单统计失败", logx.String("error", err.Error()))
	}

	return &OrderStatistics{
		TotalOrders:     totalOrders,
		PendingOrders:   pendingOrders,
		CompletedOrders: completedOrders,
		CanceledOrders:  canceledOrders,
		TotalAmount:     totalAmount,
		AvgOrderAmount:  avgOrderAmount,
		TodayOrders:     todayOrders,
		TodayAmount:     todayAmount,
	}, nil
}

// getRecentActivities 获取最近活动（内部方法）
func (ss *StatisticsService) getRecentActivities() []*RecentActivity {
	activities := make([]*RecentActivity, 0)

	// 获取最近10个订单
	orders, err := ss.svcCtx.Query.Orders.
		Order(ss.svcCtx.Query.Orders.CreatedAt.Desc()).
		Limit(10).Find()

	if err == nil {
		for _, order := range orders {
			activities = append(activities, &RecentActivity{
				Type:        "order_created",
				Description: fmt.Sprintf("创建订单: %s, 金额: %.2f", order.OrderID, order.Amount),
				Data: map[string]interface{}{
					"order_id":     order.OrderID,
					"amount":       order.Amount,
					"chain_name":   order.ChainName,
					"merchants_id": order.MerchantsID,
				},
				Timestamp: order.CreatedAt.Unix(),
			})
		}
	}

	return activities
}
