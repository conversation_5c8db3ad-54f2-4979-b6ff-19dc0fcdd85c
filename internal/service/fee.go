package service

import (
	"errors"
	"payAPI/dal"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"payAPI/model/entity"
	"time"

	"github.com/shopspring/decimal"
)

// FeeService 手续费管理服务
type FeeService struct {
	svcCtx         *svc.ServiceContext
	balanceService *BalanceService
}

// NewFeeService 创建手续费服务实例
func NewFeeService(svcCtx *svc.ServiceContext) *FeeService {
	return &FeeService{
		svcCtx:         svcCtx,
		balanceService: NewBalanceService(svcCtx),
	}
}

// FeeConfig 手续费配置
type FeeConfig struct {
	ChainName    string  `json:"chain_name"`    // 链名称
	BaseFee      float64 `json:"base_fee"`      // 基础手续费
	RateFee      float64 `json:"rate_fee"`      // 费率 (百分比)
	MinFee       float64 `json:"min_fee"`       // 最小手续费
	MaxFee       float64 `json:"max_fee"`       // 最大手续费
	FreeThreshold float64 `json:"free_threshold"` // 免费阈值
}

// 预定义手续费配置
var defaultFeeConfigs = map[string]FeeConfig{
	"TRC20": {
		ChainName:     "TRC20",
		BaseFee:       28.0,  // TRX
		RateFee:       0.001, // 0.1%
		MinFee:        10.0,
		MaxFee:        100.0,
		FreeThreshold: 1000.0, // 大于1000U免手续费
	},
	"ERC20": {
		ChainName:     "ERC20",
		BaseFee:       0.002, // ETH
		RateFee:       0.002, // 0.2%
		MinFee:        0.001,
		MaxFee:        0.01,
		FreeThreshold: 1000.0,
	},
	"BSC20": {
		ChainName:     "BSC20",
		BaseFee:       0.01, // BNB
		RateFee:       0.001,
		MinFee:        0.005,
		MaxFee:        0.05,
		FreeThreshold: 1000.0,
	},
}

// CalculateTransactionFee 计算交易手续费
func (fs *FeeService) CalculateTransactionFee(chainName string, amount float64) float64 {
	config, exists := defaultFeeConfigs[chainName]
	if !exists {
		// 默认配置
		config = FeeConfig{
			ChainName: chainName,
			BaseFee:   10.0,
			RateFee:   0.001,
			MinFee:    5.0,
			MaxFee:    50.0,
		}
	}

	// 检查是否超过免费阈值
	if amount > config.FreeThreshold {
		return 0.0
	}

	// 计算费率手续费
	amountDecimal := decimal.NewFromFloat(amount)
	rateDecimal := decimal.NewFromFloat(config.RateFee)
	rateFee, _ := amountDecimal.Mul(rateDecimal).Float64()

	// 取基础手续费和费率手续费的较大值
	fee := config.BaseFee
	if rateFee > fee {
		fee = rateFee
	}

	// 应用最小和最大限制
	if fee < config.MinFee {
		fee = config.MinFee
	}
	if fee > config.MaxFee {
		fee = config.MaxFee
	}

	return fee
}

// CheckAndDeductFee 检查并扣除手续费
func (fs *FeeService) CheckAndDeductFee(merchantsID int64, chainName string, orderAmount float64) (float64, error) {
	// 计算手续费
	feeAmount := fs.CalculateTransactionFee(chainName, orderAmount)
	
	if feeAmount == 0 {
		logx.Info("订单金额超过免费阈值，无需扣除手续费",
			logx.Int64("merchants_id", merchantsID),
			logx.String("chain_name", chainName),
			logx.Float64("amount", orderAmount))
		return 0.0, nil
	}

	// 检查手续费余额是否充足
	sufficient, err := fs.balanceService.CheckSufficientFeeBalance(merchantsID, chainName, feeAmount)
	if err != nil {
		return 0.0, err
	}

	if !sufficient {
		return 0.0, errors.New("手续费余额不足")
	}

	// 扣除手续费
	err = fs.DeductFeeBalance(merchantsID, chainName, feeAmount)
	if err != nil {
		return 0.0, err
	}

	logx.Info("手续费扣除成功",
		logx.Int64("merchants_id", merchantsID),
		logx.String("chain_name", chainName),
		logx.Float64("order_amount", orderAmount),
		logx.Float64("fee_amount", feeAmount))

	return feeAmount, nil
}

// DeductFeeBalance 扣除手续费余额
func (fs *FeeService) DeductFeeBalance(merchantsID int64, chainName string, feeAmount float64) error {
	// 使用高精度计算
	deductAmount := decimal.NewFromFloat(feeAmount)

	// 原子性扣除手续费余额
	err := fs.svcCtx.Query.Transaction(func(tx *dal.Query) error {
		balance, err := tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
			tx.MerchantsChainBalance.ChainName.Eq(chainName),
		).First()
		
		if err != nil {
			return err
		}

		// 检查手续费余额是否充足
		if balance.FeeBalance < feeAmount {
			return errors.New("手续费余额不足")
		}

		// 扣除手续费余额
		newFeeBalance, _ := decimal.NewFromFloat(balance.FeeBalance).Sub(deductAmount).Float64()

		// 更新余额
		_, err = tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.ID.Eq(balance.ID),
		).Updates(&entity.MerchantsChainBalance{
			FeeBalance:  newFeeBalance,
			LastUpdated: time.Now(),
		})

		return err
	})

	if err != nil {
		logx.Error("扣除手续费失败",
			logx.Int64("merchants_id", merchantsID),
			logx.String("chain_name", chainName),
			logx.Float64("fee_amount", feeAmount),
			logx.String("error", err.Error()))
		return err
	}

	return nil
}

// RefundFeeBalance 退还手续费余额（订单取消时）
func (fs *FeeService) RefundFeeBalance(merchantsID int64, chainName string, feeAmount float64) error {
	if feeAmount == 0 {
		return nil // 无需退还
	}

	// 使用高精度计算
	refundAmount := decimal.NewFromFloat(feeAmount)

	// 原子性增加手续费余额
	err := fs.svcCtx.Query.Transaction(func(tx *dal.Query) error {
		balance, err := tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.MerchantsID.Eq(merchantsID),
			tx.MerchantsChainBalance.ChainName.Eq(chainName),
		).First()
		
		if err != nil {
			return err
		}

		// 增加手续费余额
		newFeeBalance, _ := decimal.NewFromFloat(balance.FeeBalance).Add(refundAmount).Float64()

		// 更新余额
		_, err = tx.MerchantsChainBalance.Where(
			tx.MerchantsChainBalance.ID.Eq(balance.ID),
		).Updates(&entity.MerchantsChainBalance{
			FeeBalance:  newFeeBalance,
			LastUpdated: time.Now(),
		})

		return err
	})

	if err != nil {
		logx.Error("退还手续费失败",
			logx.Int64("merchants_id", merchantsID),
			logx.String("chain_name", chainName),
			logx.Float64("fee_amount", feeAmount),
			logx.String("error", err.Error()))
		return err
	}

	logx.Info("手续费退还成功",
		logx.Int64("merchants_id", merchantsID),
		logx.String("chain_name", chainName),
		logx.Float64("fee_amount", feeAmount))

	return nil
}

// GetFeeConfig 获取手续费配置
func (fs *FeeService) GetFeeConfig(chainName string) FeeConfig {
	config, exists := defaultFeeConfigs[chainName]
	if !exists {
		// 返回默认配置
		return FeeConfig{
			ChainName: chainName,
			BaseFee:   10.0,
			RateFee:   0.001,
			MinFee:    5.0,
			MaxFee:    50.0,
		}
	}
	return config
}

// GetAllFeeConfigs 获取所有手续费配置
func (fs *FeeService) GetAllFeeConfigs() map[string]FeeConfig {
	return defaultFeeConfigs
}

// EstimateFee 估算手续费（不扣除）
func (fs *FeeService) EstimateFee(chainName string, amount float64) (float64, FeeConfig) {
	config := fs.GetFeeConfig(chainName)
	fee := fs.CalculateTransactionFee(chainName, amount)
	return fee, config
}

// BatchCheckFeeBalance 批量检查手续费余额
func (fs *FeeService) BatchCheckFeeBalance(merchantsID int64, requirements []FeeRequirement) (bool, map[string]float64, error) {
	balanceMap := make(map[string]float64)
	allSufficient := true

	for _, req := range requirements {
		balance, err := fs.balanceService.GetMerchantBalance(merchantsID, req.ChainName)
		if err != nil {
			return false, nil, err
		}

		balanceMap[req.ChainName] = balance.FeeBalance
		
		if balance.FeeBalance < req.FeeAmount {
			allSufficient = false
		}
	}

	return allSufficient, balanceMap, nil
}

// FeeRequirement 手续费需求
type FeeRequirement struct {
	ChainName string  `json:"chain_name"` // 链名称
	FeeAmount float64 `json:"fee_amount"` // 需要的手续费金额
}