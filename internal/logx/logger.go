package logx

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

var log = logrus.New()

// OrderedMap 有序map
type OrderedMap struct {
	Order   []string
	MapData map[string]interface{}
}

// MarshalJSON 实现json.Marshaler接口
func (om OrderedMap) MarshalJSON() ([]byte, error) {
	var pairs []string
	for _, k := range om.Order {
		v, ok := om.MapData[k]
		if !ok {
			continue
		}
		pair, err := json.Marshal(v)
		if err != nil {
			return nil, err
		}
		pairs = append(pairs, fmt.Sprintf("%q:%s", k, string(pair)))
	}
	return []byte("{" + string([]byte(strings.Join(pairs, ","))) + "}"), nil
}

// CustomJSONFormatter 自定义JSON格式化器
type CustomJSONFormatter struct {
	TimestampFormat string
}

// Format 实现logrus.Formatter接口
func (f *CustomJSONFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	// 创建有序map
	om := OrderedMap{
		Order:   make([]string, 0, len(entry.Data)+3),
		MapData: make(map[string]interface{}, len(entry.Data)+3),
	}

	// 1. 添加固定顺序的字段
	om.Order = append(om.Order, "level", "msg", "time")
	om.MapData["level"] = entry.Level.String()
	om.MapData["msg"] = entry.Message
	om.MapData["time"] = entry.Time.Format(f.TimestampFormat)

	// 2. 对其他字段进行排序
	var keys []string
	for k := range entry.Data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 3. 按顺序添加其他字段
	for _, k := range keys {
		om.Order = append(om.Order, k)
		om.MapData[k] = entry.Data[k]
	}

	// 4. 序列化并添加换行符
	serialized, err := json.Marshal(om)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal fields to JSON, %v", err)
	}

	return append(serialized, '\n'), nil
}

// Config 日志配置
type Config struct {
	Path           string // 日志文件路径
	Level          string // 日志级别
	Mode           string // 日志模式 (file/console)
	ConsoleLogging bool   // 是否输出到控制台
	FileTimeFormat string // 日志文件时间格式
	TimeFormat     string // 日志时间格式
	KeepDays       int    // 日志保留天数
	Compress       bool   // 是否压缩
}

// Setup 初始化日志系统
func Setup(cfg *Config) error {
	// 创建日志目录
	if err := os.MkdirAll(cfg.Path, 0755); err != nil {
		return fmt.Errorf("create log directory failed: %v", err)
	}

	// 设置日志格式
	log.SetFormatter(&CustomJSONFormatter{
		TimestampFormat: cfg.TimeFormat,
	})

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	log.SetLevel(level)

	// 设置输出
	writers := make([]io.Writer, 0, 2)

	// 文件输出
	if cfg.Mode == "file" {
		fileWriter := &lumberjack.Logger{
			Filename:   filepath.Join(cfg.Path, fmt.Sprintf("app.%s.log", time.Now().Format(cfg.FileTimeFormat))),
			MaxSize:    100,          // 每个日志文件最大尺寸（MB）
			MaxBackups: 30,           // 保留的旧日志文件最大数量
			MaxAge:     cfg.KeepDays, // 保留的旧日志文件最大天数
			Compress:   cfg.Compress, // 是否压缩旧日志文件
		}
		writers = append(writers, fileWriter)
	}

	// 控制台输出
	if cfg.ConsoleLogging {
		writers = append(writers, os.Stdout)
	}

	// 如果有多个输出，使用MultiWriter
	if len(writers) > 0 {
		log.SetOutput(io.MultiWriter(writers...))
	}

	return nil
}

// MustSetup 初始化日志系统，如果失败则panic
func MustSetup(cfg *Config) {
	if err := Setup(cfg); err != nil {
		panic(err)
	}
}

// Sync 刷新缓冲的日志
func Sync() error {
	return nil // logrus不需要手动同步
}

// 字段创建函数
func String(key string, val string) logrus.Fields {
	return logrus.Fields{key: val}
}

func Int(key string, val int) logrus.Fields {
	return logrus.Fields{key: val}
}

func Int64(key string, val int64) logrus.Fields {
	return logrus.Fields{key: val}
}

func Float64(key string, val float64) logrus.Fields {
	return logrus.Fields{key: val}
}

func Bool(key string, val bool) logrus.Fields {
	return logrus.Fields{key: val}
}

func Any(key string, val interface{}) logrus.Fields {
	return logrus.Fields{key: val}
}

// mergFields 合并多个Fields
func mergFields(fields ...logrus.Fields) logrus.Fields {
	result := make(logrus.Fields)
	for _, field := range fields {
		for k, v := range field {
			result[k] = v
		}
	}
	return result
}

// getContextFields 从fiber.Ctx中获取上下文字段
func getContextFields(c *fiber.Ctx) logrus.Fields {
	fields := logrus.Fields{}

	// 从context中获取基本信息
	if requestID := c.Locals("requestId"); requestID != nil {
		fields["request_id"] = requestID
	}
	if route := c.Locals("route"); route != nil {
		fields["route"] = route
	}
	if method := c.Locals("method"); method != nil {
		fields["method"] = method
	}
	if clientIP := c.Locals("clientIp"); clientIP != nil {
		fields["client_ip"] = clientIP
	}
	if userAgent := c.Locals("userAgent"); userAgent != nil {
		fields["user_agent"] = userAgent
	}
	if userID := c.Locals("userId"); userID != nil {
		fields["user_id"] = userID
	}
	if body := c.Locals("requestBody"); body != nil {
		bodyJSON, _ := json.Marshal(body)
		fields["request_body"] = string(bodyJSON)
	}

	return fields
}

// Debug 不带上下文的日志函数
func Debug(format string, args ...interface{}) {
	log.Debug(fmt.Sprintf(format, args...))
}

func Info(format string, args ...interface{}) {
	log.Info(fmt.Sprintf(format, args...))
}

func Warn(format string, args ...interface{}) {
	log.Warn(fmt.Sprintf(format, args...))
}

func Error(format string, args ...interface{}) {
	log.Error(fmt.Sprintf(format, args...))
}

func Fatal(format string, args ...interface{}) {
	log.Fatal(fmt.Sprintf(format, args...))
}

// WithFields 添加字段
func WithFields(fields logrus.Fields) *logrus.Entry {
	return log.WithFields(fields)
}

// Debugf 带上下文的格式化日志函数
func Debugf(c *fiber.Ctx, format string, args ...interface{}) {
	log.WithFields(getContextFields(c)).Debug(fmt.Sprintf(format, args...))
}

func Infof(c *fiber.Ctx, format string, args ...interface{}) {
	log.WithFields(getContextFields(c)).Info(fmt.Sprintf(format, args...))
}

func Warnf(c *fiber.Ctx, format string, args ...interface{}) {
	log.WithFields(getContextFields(c)).Warn(fmt.Sprintf(format, args...))
}

func Errorf(c *fiber.Ctx, format string, args ...interface{}) {
	fields := getContextFields(c)

	// 获取调用栈信息
	_, file, line, ok := runtime.Caller(1)
	if ok {
		fields["file"] = file
		fields["line"] = line
	}

	log.WithFields(fields).Error(fmt.Sprintf(format, args...))
}

func Fatalf(c *fiber.Ctx, format string, args ...interface{}) {
	fields := getContextFields(c)

	// 获取调用栈信息
	_, file, line, ok := runtime.Caller(1)
	if ok {
		fields["file"] = file
		fields["line"] = line
	}

	log.WithFields(fields).Fatal(fmt.Sprintf(format, args...))
}
