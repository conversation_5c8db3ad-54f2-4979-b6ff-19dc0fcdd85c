package logx

// Config 日志配置
type onfig struct {
	Mode           string `yaml:"Mode"`           // 日志模式：file/console
	Path           string `yaml:"Path"`           // 日志路径
	Level          string `yaml:"Level"`          // 日志级别
	Rotation       string `yaml:"Rotation"`       // 日志切割方式
	KeepDays       int    `yaml:"KeepDays"`       // 保留天数
	Compress       bool   `yaml:"Compress"`       // 是否压缩
	FileTimeFormat string `yaml:"FileTimeFormat"` // 文件时间格式
	TimeFormat     string `yaml:"TimeFormat"`     // 日志时间格式
	ConsoleLogging bool   `yaml:"ConsoleLogging"` // 是否输出到控制台
}
