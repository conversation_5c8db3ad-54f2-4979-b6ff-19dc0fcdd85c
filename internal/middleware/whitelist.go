package middleware

import (
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"strings"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
)

// IPWhitelistCache IP白名单缓存
type IPWhitelistCache struct {
	whitelist map[string]bool
	mutex     sync.RWMutex
	lastSync  time.Time
}

var (
	ipCache      *IPWhitelistCache
	cacheOnce    sync.Once
	syncInterval = 5 * time.Minute // 5分钟同步一次
)

// initIPCache 初始化IP缓存
func initIPCache() {
	ipCache = &IPWhitelistCache{
		whitelist: make(map[string]bool),
	}
}

// IPWhitelistMiddleware IP白名单中间件
func IPWhitelistMiddleware(svcCtx *svc.ServiceContext) fiber.Handler {
	// 确保缓存初始化
	cacheOnce.Do(initIPCache)

	return func(c *fiber.Ctx) error {
		clientIP := c.IP()
		logx.Info("IP访问验证 %v", clientIP)
		// 检查IP是否在白名单中
		if !isIPWhitelisted(clientIP, svcCtx) {
			logx.Warn("IP访问被拒绝", logx.String("ip", clientIP), logx.String("path", c.Path()))
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code": 403,
				"msg":  "访问被拒绝",
				"data": nil,
			})
		}

		logx.Debug("IP白名单验证通过", logx.String("ip", clientIP))
		return c.Next()
	}
}

// isIPWhitelisted 检查IP是否在白名单中
func isIPWhitelisted(ip string, svcCtx *svc.ServiceContext) bool {
	ipCache.mutex.RLock()
	//logx.Info("检查IP白名单 - 当前IP: %s", ip)
	//logx.Info("当前白名单缓存内容: %v", ipCache.whitelist)
	//logx.Info("缓存大小: %d", len(ipCache.whitelist))
	//logx.Info("上次同步时间: %v", ipCache.lastSync)

	// 检查是否需要刷新缓存
	needRefresh := time.Since(ipCache.lastSync) > syncInterval || len(ipCache.whitelist) == 0
	ipCache.mutex.RUnlock()
	//
	//logx.Info("是否需要刷新缓存: %t", needRefresh)

	if needRefresh {
		//logx.Info("开始刷新IP白名单缓存")
		refreshIPWhitelist(svcCtx)
	}

	ipCache.mutex.RLock()
	defer ipCache.mutex.RUnlock()

	isWhitelisted := ipCache.whitelist[ip]
	//logx.Info("IP %s 在白名单中: %t", ip, isWhitelisted)
	logx.Info("最终白名单内容: %v", ipCache.whitelist)

	return isWhitelisted
}

// refreshIPWhitelist 刷新IP白名单缓存
func refreshIPWhitelist(svcCtx *svc.ServiceContext) {
	//logx.Info("开始从数据库查询IP白名单")

	// 先查询所有白名单记录看看有什么
	//allWhitelists, err := svcCtx.Query.WhiteList.Find()
	//if err == nil {
	//	//logx.Info("数据库中所有白名单记录:")
	//	//for _, wl := range allWhitelists {
	//	//	//logx.Info("记录: ID=%d, IP=%s, Type=%d", wl.ID, wl.IP, wl.Type)
	//	//}
	//}

	// 从数据库查询白名单IP - 修改为查询Type=2
	whitelists, err := svcCtx.Query.WhiteList.Where(
		svcCtx.Query.WhiteList.Type.Eq(2), // 2表示API访问白名单
	).Find()

	if err != nil {
		logx.Error("查询IP白名单失败: %v", err)
		return
	}

	//logx.Info("从数据库查询到 %d 条白名单记录", len(whitelists))

	ipCache.mutex.Lock()
	defer ipCache.mutex.Unlock()

	// 清空旧缓存
	ipCache.whitelist = make(map[string]bool)

	// 填充新的白名单
	for _, wl := range whitelists {
		//logx.Info("处理白名单记录 - IP: %s, Type: %d", wl.IP, wl.Type)
		if wl.IP != "" {
			// 处理CIDR格式，提取纯IP部分
			ip := wl.IP
			if strings.Contains(ip, "/") {
				// 如果是CIDR格式，提取IP部分
				parts := strings.Split(ip, "/")
				if len(parts) > 0 {
					ip = parts[0]
				}
			}

			// 同时添加原格式和纯IP格式
			ipCache.whitelist[wl.IP] = true // 原格式
			ipCache.whitelist[ip] = true    // 纯IP格式

			//logx.Info("添加IP到白名单: %s (原格式: %s)", ip, wl.IP)
		}
	}

	ipCache.lastSync = time.Now()
	//logx.Info("IP白名单缓存刷新完成 - 总计: %d 个IP", len(ipCache.whitelist))
	//logx.Info("刷新后的白名单内容: %v", ipCache.whitelist)
}

// ForceRefreshWhitelist 强制刷新白名单缓存（用于管理接口）
func ForceRefreshWhitelist(svcCtx *svc.ServiceContext) {
	refreshIPWhitelist(svcCtx)
}
