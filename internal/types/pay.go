package types

// ReceivePayRequest 收款下单接口 /Api/receivePay
// 签名字段按定义顺序: merchants_id + amount + merchant_order_id
type ReceivePayRequest struct {
	MerchantsID     int64   `json:"merchants_id" validate:"required"`      // 商户ID
	Amount          float64 `json:"amount" validate:"required"`            // 金额
	MerchantOrderId string  `json:"merchant_order_id" validate:"required"` // 商户订单号
	CallbackURL     string  `json:"callback_url"`                          // 回调地址
	ReturnURL       string  `json:"return_url"`                            // 收银台跳转地址
	Chain           string  `json:"chain"`                                 // 链 Trc,Erc,Bsc 默认Trc
	Currency        string  `json:"currency"`                              // 链 Trc,Erc,Bsc 默认Trc
	UserId          string  `json:"user_id"`                               // 链 Trc,Erc,Bsc 默认Trc
}

// ReceivePayQueryRequest 查询收款订单 /Api/receivePayQuery
// 签名字段按定义顺序: merchants_id + orderid
type ReceivePayQueryRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	OrderID     string `json:"order_id" validate:"required"`     // 系统订单号
}

// IssuedPayRequest 下发下单接口 /Api/issuedPay
// 签名字段按定义顺序: merchants_id + money + merchant_order_id + address
type IssuedPayRequest struct {
	MerchantsID     int64   `json:"merchants_id" validate:"required"`      // 商户ID
	Amount          float64 `json:"amount" validate:"required"`            // 金额
	MerchantOrderId string  `json:"merchant_order_id" validate:"required"` // 商户订单号
	Address         string  `json:"address" validate:"required"`           // 收款地址
	CallbackURL     string  `json:"callback_url"`                          // 回调地址
	Chain           string  `json:"chain"`                                 // 链 Trc,Erc,Bsc
}

// IssuedPayQueryRequest 查询下发订单 /Api/issuedPayQuery
// 签名字段按定义顺序: merchants_id + order_id
type IssuedPayQueryRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	OrderID     string `json:"order_id" validate:"required"`     // 系统订单号
}

// BindAddressRequest 绑定用户地址 /Api/bindAddress
// 签名字段按定义顺序: merchants_id + user_id + trc_address + erc_address
type BindAddressRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	UserID      string `json:"user_id" validate:"required"`      // 用户ID
	TrcAddress  string `json:"trc_address" validate:"required"`  // TRC地址
	ErcAddress  string `json:"erc_address" validate:"required"`  // ERC地址
	Remark      string `json:"remark"`                           // 备注
}

// UpdateCallbackURLRequest 更新回调地址 /Api/UpdateCallbackURL
// 签名字段按定义顺序: merchants_id + order_id + callback_url
type UpdateCallbackURLRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	OrderID     string `json:"order_id" validate:"required"`     // 系统订单号
	CallbackURL string `json:"callback_url" validate:"required"` // 新的回调地址
}

// UpdateAddressRequest 更新用户地址 /Api/UpdateAddress
// 签名字段按定义顺序: merchants_id + user_id + trc_address + erc_address
type UpdateAddressRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	UserID      string `json:"user_id" validate:"required"`      // 用户ID
	TrcAddress  string `json:"trc_address"`                      // 新的TRC地址(可选)
	ErcAddress  string `json:"erc_address"`                      // 新的ERC地址(可选)
	Remark      string `json:"remark"`                           // 备注
}

// ReceivePayCancelRequest 绑定用户地址 /Api/ReceivePayCancel
// 签名字段按定义顺序: merchants_id + order_id
type ReceivePayCancelRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	OrderID     string `json:"order_id" validate:"required"`     // 系统订单号
}

// IssuedPayCancelRequest 绑定用户地址 /Api/ReceivePayCancel
// 签名字段按定义顺序: merchants_id + order_id
type IssuedPayCancelRequest struct {
	MerchantsID int64  `json:"merchants_id" validate:"required"` // 商户ID
	OrderID     string `json:"order_id" validate:"required"`     // 系统订单号
}

// CallBackUrlRequest 绑定用户地址 /Api/CallBackUrlRequest
type CallBackUrlRequest struct {
}
