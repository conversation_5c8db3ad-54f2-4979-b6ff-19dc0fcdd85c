package websocket

import "time"

// MessageType WebSocket消息类型
type MessageType string

const (
	// 地址相关消息
	MessageTypeAddTRC MessageType = "addtrc" // 添加TRC地址
	MessageTypeAddERC MessageType = "adderc" // 添加ERC地址
	
	// 订单相关消息
	MessageTypeOrderCreated   MessageType = "order_created"   // 订单创建
	MessageTypeOrderPaid      MessageType = "order_paid"      // 订单支付
	MessageTypeOrderTimeout   MessageType = "order_timeout"   // 订单超时
	MessageTypeOrderCanceled  MessageType = "order_canceled"  // 订单取消
	
	// 系统消息
	MessageTypeSystemNotice   MessageType = "system_notice"   // 系统通知
	MessageTypeHeartbeat      MessageType = "heartbeat"       // 心跳消息
)

// Message WebSocket消息结构
type Message struct {
	Type      MessageType `json:"type"`              // 消息类型
	Data      interface{} `json:"data"`              // 消息数据
	Timestamp int64       `json:"timestamp"`         // 时间戳
	MessageID string      `json:"message_id"`        // 消息ID
}

// AddressMessage 地址相关消息
type AddressMessage struct {
	Address string `json:"address"` // 地址
}

// OrderMessage 订单相关消息
type OrderMessage struct {
	OrderID         string  `json:"order_id"`          // 订单号
	MerchantOrderID string  `json:"merchant_order_id"` // 商户订单号
	Address         string  `json:"address"`           // 收款地址
	Amount          float64 `json:"amount"`            // 金额
	Chain           string  `json:"chain"`             // 链类型
	Status          int32   `json:"status"`            // 状态
	MerchantsID     int64   `json:"merchants_id"`      // 商户ID
}

// SystemMessage 系统消息
type SystemMessage struct {
	Title   string `json:"title"`   // 标题
	Content string `json:"content"` // 内容
	Level   string `json:"level"`   // 级别：info, warning, error
}

// HeartbeatMessage 心跳消息
type HeartbeatMessage struct {
	Timestamp int64  `json:"timestamp"` // 时间戳
	ServerID  string `json:"server_id"` // 服务器ID
}

// NewMessage 创建新消息
func NewMessage(msgType MessageType, data interface{}) *Message {
	return &Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
		MessageID: generateMessageID(),
	}
}

// generateMessageID 生成消息ID
func generateMessageID() string {
	return time.Now().Format("20060102150405") + "-" + generateRandomString(6)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}