package websocket

import (
	"encoding/json"
	"payAPI/internal/logx"
	"sync"
	"time"

	"github.com/gofiber/contrib/websocket"
)

// Client WebSocket客户端
type Client struct {
	ID       string          `json:"id"`        // 客户端ID
	Conn     *websocket.Conn `json:"-"`         // WebSocket连接
	Send     chan []byte     `json:"-"`         // 发送消息通道
	Hub      *Hub            `json:"-"`         // 所属Hub
	LastPing time.Time       `json:"last_ping"` // 最后心跳时间
	UserInfo map[string]interface{} `json:"user_info"` // 用户信息
}

// Hub WebSocket连接管理器
type Hub struct {
	clients    map[*Client]bool // 活跃客户端
	broadcast  chan []byte      // 广播消息通道
	register   chan *Client     // 客户端注册通道
	unregister chan *Client     // 客户端注销通道
	mutex      sync.RWMutex     // 读写锁
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte, 256),
		register:   make(chan *Client),
		unregister: make(chan *Client),
	}
}

// Run 运行Hub
func (h *Hub) Run() {
	logx.Info("WebSocket Hub 已启动")
	
	// 启动心跳检测
	go h.heartbeatChecker()
	
	for {
		select {
		case client := <-h.register:
			// 注册新客户端
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			
			logx.Info("WebSocket客户端连接", 
				logx.String("client_id", client.ID),
				logx.Int("total_clients", h.GetClientCount()))
			
			// 发送欢迎消息
			welcomeMsg := NewMessage(MessageTypeSystemNotice, SystemMessage{
				Title:   "连接成功",
				Content: "WebSocket连接已建立",
				Level:   "info",
			})
			
			if msgBytes, err := json.Marshal(welcomeMsg); err == nil {
				select {
				case client.Send <- msgBytes:
				default:
					h.removeClient(client)
				}
			}

		case client := <-h.unregister:
			// 注销客户端
			if _, ok := h.clients[client]; ok {
				h.removeClient(client)
				logx.Info("WebSocket客户端断开连接",
					logx.String("client_id", client.ID),
					logx.Int("total_clients", h.GetClientCount()))
			}

		case message := <-h.broadcast:
			// 广播消息到所有客户端
			h.mutex.RLock()
			clientsCount := len(h.clients)
			successCount := 0
			
			for client := range h.clients {
				select {
				case client.Send <- message:
					successCount++
				default:
					h.removeClientUnsafe(client)
				}
			}
			h.mutex.RUnlock()
			
			logx.Debug("消息广播完成",
				logx.Int("total_clients", clientsCount),
				logx.Int("success_count", successCount))
		}
	}
}

// removeClient 安全移除客户端（带锁）
func (h *Hub) removeClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.removeClientUnsafe(client)
}

// removeClientUnsafe 移除客户端（不加锁，内部使用）
func (h *Hub) removeClientUnsafe(client *Client) {
	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.Send)
		client.Conn.Close()
	}
}

// BroadcastMessage 广播消息
func (h *Hub) BroadcastMessage(message *Message) {
	msgBytes, err := json.Marshal(message)
	if err != nil {
		logx.Error("序列化WebSocket消息失败", logx.String("error", err.Error()))
		return
	}
	
	select {
	case h.broadcast <- msgBytes:
		logx.Debug("消息已加入广播队列", logx.String("type", string(message.Type)))
	default:
		logx.Warn("广播队列已满，消息丢弃", logx.String("type", string(message.Type)))
	}
}

// SendToClient 发送消息给指定客户端
func (h *Hub) SendToClient(clientID string, message *Message) {
	msgBytes, err := json.Marshal(message)
	if err != nil {
		logx.Error("序列化WebSocket消息失败", logx.String("error", err.Error()))
		return
	}
	
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	for client := range h.clients {
		if client.ID == clientID {
			select {
			case client.Send <- msgBytes:
				logx.Debug("消息已发送给客户端", 
					logx.String("client_id", clientID),
					logx.String("type", string(message.Type)))
			default:
				logx.Warn("客户端发送队列已满", logx.String("client_id", clientID))
			}
			return
		}
	}
	
	logx.Warn("未找到指定客户端", logx.String("client_id", clientID))
}

// GetClientCount 获取客户端数量
func (h *Hub) GetClientCount() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.clients)
}

// GetClientIDs 获取所有客户端ID
func (h *Hub) GetClientIDs() []string {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	
	ids := make([]string, 0, len(h.clients))
	for client := range h.clients {
		ids = append(ids, client.ID)
	}
	return ids
}

// heartbeatChecker 心跳检测器
func (h *Hub) heartbeatChecker() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()
	
	for range ticker.C {
		now := time.Now()
		h.mutex.RLock()
		var disconnectedClients []*Client
		
		for client := range h.clients {
			// 检查客户端是否超时（2分钟无心跳）
			if now.Sub(client.LastPing) > 2*time.Minute {
				disconnectedClients = append(disconnectedClients, client)
			}
		}
		h.mutex.RUnlock()
		
		// 移除超时的客户端
		for _, client := range disconnectedClients {
			logx.Warn("客户端心跳超时，断开连接", logx.String("client_id", client.ID))
			h.unregister <- client
		}
		
		// 发送心跳消息给所有客户端
		heartbeatMsg := NewMessage(MessageTypeHeartbeat, HeartbeatMessage{
			Timestamp: now.Unix(),
			ServerID:  "payAPI-server",
		})
		h.BroadcastMessage(heartbeatMsg)
	}
}