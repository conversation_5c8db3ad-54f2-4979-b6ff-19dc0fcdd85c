package websocket

import (
	"encoding/json"
	"payAPI/internal/logx"
	"time"

	"github.com/gofiber/contrib/websocket"
)

var (
	// GlobalHub 全局WebSocket Hub
	GlobalHub *Hub
)

// InitWebSocketServer 初始化WebSocket服务器
func InitWebSocketServer() {
	GlobalHub = NewHub()
	go GlobalHub.Run()
	logx.Info("WebSocket服务器已初始化")
}

// HandleWebSocketConnection 处理WebSocket连接
func HandleWebSocketConnection(c *websocket.Conn) {
	// Fiber WebSocket连接已经升级完毕，直接使用连接

	// 创建客户端
	client := &Client{
		ID:       generateClientID(),
		Conn:     c,
		Send:     make(chan []byte, 256),
		Hub:      GlobalHub,
		LastPing: time.Now(),
		UserInfo: make(map[string]interface{}),
	}

	// 从查询参数获取用户信息 (需要从WebSocket upgrade时传递)
	// 暂时从连接中获取基本信息
	client.UserInfo["client_id"] = client.ID

	// 注册客户端到Hub
	GlobalHub.register <- client

	// 启动客户端的读写goroutine
	go client.readPump()
	go client.writePump()
}

// readPump 读取客户端消息
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	// 设置读取超时时间
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.LastPing = time.Now()
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, messageData, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logx.Error("WebSocket读取错误",
					logx.String("client_id", c.ID),
					logx.String("error", err.Error()))
			}
			break
		}

		// 解析客户端消息
		var clientMsg map[string]interface{}
		if err := json.Unmarshal(messageData, &clientMsg); err != nil {
			logx.Warn("解析客户端消息失败",
				logx.String("client_id", c.ID),
				logx.String("error", err.Error()))
			continue
		}

		// 处理客户端消息
		c.handleClientMessage(clientMsg)
	}
}

// writePump 向客户端写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second) // 54秒发送一次ping
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// Hub关闭了发送通道
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 将排队的消息一起发送
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleClientMessage 处理客户端消息
func (c *Client) handleClientMessage(msg map[string]interface{}) {
	msgType, ok := msg["type"].(string)
	if !ok {
		logx.Warn("客户端消息缺少type字段", logx.String("client_id", c.ID))
		return
	}

	switch msgType {
	case "ping":
		// 处理心跳
		c.LastPing = time.Now()
		pongMsg := NewMessage("pong", map[string]interface{}{
			"timestamp": time.Now().Unix(),
		})
		if msgBytes, err := json.Marshal(pongMsg); err == nil {
			select {
			case c.Send <- msgBytes:
			default:
				// 发送队列已满，忽略
			}
		}

	case "auth":
		// 处理认证消息
		if data, ok := msg["data"].(map[string]interface{}); ok {
			for key, value := range data {
				c.UserInfo[key] = value
			}
			logx.Info("客户端认证信息更新",
				logx.String("client_id", c.ID),
				logx.Any("user_info", c.UserInfo))
		}

	default:
		logx.Debug("收到客户端消息",
			logx.String("client_id", c.ID),
			logx.String("type", msgType))
	}
}

// generateClientID 生成客户端ID
func generateClientID() string {
	return "client_" + time.Now().Format("20060102150405") + "_" + generateRandomString(8)
}

// BroadcastToAll 广播消息给所有客户端
func BroadcastToAll(msgType MessageType, data interface{}) {
	if GlobalHub == nil {
		logx.Warn("WebSocket服务器未初始化，消息丢弃")
		return
	}

	message := NewMessage(msgType, data)
	GlobalHub.BroadcastMessage(message)
}

// SendToMerchant 发送消息给指定商户
func SendToMerchant(merchantID string, msgType MessageType, data interface{}) {
	if GlobalHub == nil {
		logx.Warn("WebSocket服务器未初始化，消息丢弃")
		return
	}

	message := NewMessage(msgType, data)
	msgBytes, err := json.Marshal(message)
	if err != nil {
		logx.Error("序列化消息失败", logx.String("error", err.Error()))
		return
	}

	sent := false
	GlobalHub.mutex.RLock()
	for client := range GlobalHub.clients {
		if merchantIDValue, exists := client.UserInfo["merchant_id"]; exists {
			if merchantIDValue == merchantID {
				select {
				case client.Send <- msgBytes:
					sent = true
					logx.Debug("消息已发送给商户",
						logx.String("merchant_id", merchantID),
						logx.String("type", string(msgType)))
				default:
					logx.Warn("商户客户端发送队列已满", logx.String("merchant_id", merchantID))
				}
			}
		}
	}
	GlobalHub.mutex.RUnlock()

	if !sent {
		logx.Debug("未找到商户的活跃连接", logx.String("merchant_id", merchantID))
	}
}

// GetServerStats 获取服务器统计信息
func GetServerStats() map[string]interface{} {
	if GlobalHub == nil {
		return map[string]interface{}{
			"initialized": false,
		}
	}

	return map[string]interface{}{
		"initialized":   true,
		"total_clients": GlobalHub.GetClientCount(),
		"client_ids":    GlobalHub.GetClientIDs(),
		"server_time":   time.Now().Unix(),
	}
}
