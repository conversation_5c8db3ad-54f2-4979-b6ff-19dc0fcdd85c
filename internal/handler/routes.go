package handler

import (
	"payAPI/internal/logic/callback"
	"payAPI/internal/logic/pay"
	"payAPI/internal/middleware"
	"payAPI/internal/svc"
	"payAPI/internal/types"
	wsserver "payAPI/internal/websocket"

	"github.com/gofiber/contrib/websocket"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

// RegisterHandlers 注册所有处理器和中间件
func RegisterHandlers(app *fiber.App, ctx *svc.ServiceContext) {
	// 全局中间件
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New())

	// 注入服务上下文到每个请求
	app.Use(func(c *fiber.Ctx) error {
		c.Locals("svcCtx", ctx)
		return c.Next()
	})

	api := app.Group("/api")
	{
		v1 := api.Group("/v1")
		// API白名单验证中间件
		v1.Use(middleware.IPWhitelistMiddleware(ctx))
		{
			// 绑定用户地址 todo
			v1.Post("/bindAddress", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.BindAddressRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.BindAddress(req)
			}))

			// 更新用户地址 todo
			v1.Post("/updateAddress", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.UpdateAddressRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.UpdateAddress(req)
			}))

			//更新回调地址 todo
			v1.Post("/updateCallbackURL", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.UpdateCallbackURLRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.UpdateCallbackURL(req)
			}))

			// 收款下单接口
			v1.Post("/receivePay", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.ReceivePayRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.ReceivePay(req)
			}))

			// 查询收款订单
			v1.Post("/receivePayQuery", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.ReceivePayQueryRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.ReceivePayQuery(req)
			}))

			// 取消收款订单  todo
			v1.Post("/receivePayCancel", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.ReceivePayCancelRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.ReceivePayCancel(req)
			}))

			// 下发下单接口  todo
			v1.Post("/issuedPay", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.IssuedPayRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.IssuedPay(req)
			}))

			// 查询下发订单  todo
			v1.Post("/issuedPayQuery", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.IssuedPayQueryRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.IssuedPayQuery(req)
			}))

			// 下发订单取消  todo
			v1.Post("/issuedPayCancel", CommonHandler(func(c *fiber.Ctx, svcCtx *svc.ServiceContext, req *types.IssuedPayCancelRequest) error {
				l := pay.NewLogic(c, svcCtx)
				return l.IssuedPayCancel(req)
			}))

			// chain_eye 回调通知接口
			v1.Post("/callBackUrl", func(c *fiber.Ctx) error {
				svcCtx := c.Locals("svcCtx").(*svc.ServiceContext)
				l := callback.NewLogic(c, svcCtx)
				result := l.CallBackFun(c, svcCtx)
				return c.SendString(result)
			})

		}
	}

	// WebSocket路由 (不需要白名单验证)
	app.Get("/ws", func(c *fiber.Ctx) error {
		// 检查是否为WebSocket升级请求
		if websocket.IsWebSocketUpgrade(c) {
			return websocket.New(wsserver.HandleWebSocketConnection)(c)
		}
		return fiber.ErrUpgradeRequired
	})

	// WebSocket状态查询API
	app.Get("/ws/stats", func(c *fiber.Ctx) error {
		stats := wsserver.GetServerStats()
		return c.JSON(fiber.Map{
			"code": 200,
			"msg":  "success",
			"data": stats,
		})
	})
}
