package handler

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"payAPI/internal/logx"
	"payAPI/internal/svc"
	"reflect"
	"strconv"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

var validate = validator.New()

// 初始化验证器
func init() {
	// 注册自定义错误信息翻译
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})
}

// validateRequest 通用的请求验证函数
func validateRequest[T any](c *fiber.Ctx) (*T, error) {
	var req T

	// 如果不是POST请求，直接返回空结构体
	if c.Method() != "POST" {
		return &req, nil
	}

	// 如果请求体为空，返回空结构体
	if c.Request().Body() == nil || len(c.Request().Body()) == 0 {
		return &req, nil
	}

	// 解析请求体
	if err := c.BodyParser(&req); err != nil {
		return nil, fmt.Errorf("无效的请求参数")
	}

	// 验证请求体
	if err := validate.Struct(req); err != nil {
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			// 只返回第一个错误
			for _, e := range validationErrors {
				logx.Info("%s不能为空", e.Field())
				return nil, fmt.Errorf("缺少必要参数")
			}
		}
		return nil, fmt.Errorf("参数验证错误")
	}

	return &req, nil
}

// CommonHandler 通用处理器
func CommonHandler[T any](handler func(*fiber.Ctx, *svc.ServiceContext, *T) error) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取服务上下文
		svcCtx := c.Locals("svcCtx").(*svc.ServiceContext)

		// 验证和解析请求
		req, err := validateRequest[T](c)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"code": fiber.StatusBadRequest,
				"msg":  err.Error(),
			})
		}

		// 签名验证（针对PayRequest类型）
		authHeader := c.Get("Authorization")
		if err := verifySignature(req, authHeader, svcCtx); err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code": fiber.StatusUnauthorized,
				"msg":  "签名验证失败: " + err.Error(),
			})
		}

		// 执行处理函数
		if err := handler(c, svcCtx, req); err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code": fiber.StatusInternalServerError,
				"msg":  err.Error(),
			})
		}

		return nil
	}
}

// verifySignature 验证签名
func verifySignature(req interface{}, authHeader string, svcCtx *svc.ServiceContext) error {
	// 检查是否需要签名验证
	if !needsSignatureVerification(req) {
		return nil
	}

	// 检查Authorization header
	if authHeader == "" {
		return fmt.Errorf("缺少Authorization header")
	}

	// 提取必传参数并按顺序拼接
	signString, merchantID, err := buildSignatureString(req)
	if err != nil {
		return fmt.Errorf("构建签名字符串失败: %v", err)
	}

	// 通过merchants_id查询商户的appkey
	appKey, err := getMerchantAppKey(merchantID, svcCtx)
	if err != nil {
		return fmt.Errorf("获取商户appkey失败: %v", err)
	}

	// 计算签名
	expectedSignature := calculateHMACSHA256(signString, appKey)
	logx.Info("签名为：%s", expectedSignature)

	// 比较签名
	if authHeader != expectedSignature {
		return fmt.Errorf("签名验证失败")
	}

	return nil
}

// needsSignatureVerification 检查是否需要签名验证
func needsSignatureVerification(req interface{}) bool {
	if req == nil {
		return false
	}

	// 使用反射检查结构体是否包含 merchants_id 字段
	reqValue := reflect.ValueOf(req)
	reqType := reflect.TypeOf(req)

	// 如果是指针，获取指向的元素
	if reqValue.Kind() == reflect.Ptr {
		reqValue = reqValue.Elem()
		reqType = reqType.Elem()
	}

	if reqValue.Kind() != reflect.Struct {
		return false
	}

	// 检查是否有 merchants_id 字段且标记为 required
	for i := 0; i < reqValue.NumField(); i++ {
		field := reqType.Field(i)
		jsonTag := field.Tag.Get("json")
		validateTag := field.Tag.Get("validate")

		if jsonTag != "" {
			jsonName := strings.Split(jsonTag, ",")[0]
			if jsonName == "merchants_id" && strings.Contains(validateTag, "required") {
				return true
			}
		}
	}

	return false
}

// buildSignatureString 构建签名字符串
func buildSignatureString(req interface{}) (string, string, error) {
	if req == nil {
		return "", "", fmt.Errorf("请求参数为空")
	}

	// 使用反射获取结构体信息
	reqValue := reflect.ValueOf(req)
	reqType := reflect.TypeOf(req)

	// 如果是指针，获取指向的元素
	if reqValue.Kind() == reflect.Ptr {
		reqValue = reqValue.Elem()
		reqType = reqType.Elem()
	}

	if reqValue.Kind() != reflect.Struct {
		return "", "", fmt.Errorf("请求参数必须是结构体")
	}

	var signParts []string
	var merchantsId int64

	// 遍历结构体字段，按定义顺序处理
	for i := 0; i < reqValue.NumField(); i++ {
		field := reqType.Field(i)
		fieldValue := reqValue.Field(i)

		// 检查字段是否有 validate:"required" 标签
		validateTag := field.Tag.Get("validate")
		if !strings.Contains(validateTag, "required") {
			continue
		}

		// 根据字段的实际类型直接获取值并转换为字符串用于签名
		var fieldStr string
		jsonTag := field.Tag.Get("json")
		jsonName := ""
		if jsonTag != "" {
			jsonName = strings.Split(jsonTag, ",")[0]
		}

		switch fieldValue.Kind() {
		case reflect.Int64:
			int64Val := fieldValue.Int()
			fieldStr = strconv.FormatInt(int64Val, 10)

			// 如果是 merchants_id 字段，保存用于查询
			if jsonName == "merchants_id" {
				merchantsId = int64Val
			}

		case reflect.Float64:
			float64Val := fieldValue.Float()

			// 检查是否是 merchants_id 字段，如果是且为整数，转换为int64
			if jsonName == "merchants_id" {
				if float64Val == float64(int64(float64Val)) {
					merchantsId = int64(float64Val)
					fieldStr = strconv.FormatInt(merchantsId, 10)
				} else {
					return "", "", fmt.Errorf("merchants_id 字段值不是有效的整数: %f", float64Val)
				}
			} else {
				fieldStr = strconv.FormatFloat(float64Val, 'f', -1, 64)
			}

		case reflect.String:
			fieldStr = fieldValue.String()

		default:
			// 对于其他类型，使用默认转换
			fieldStr = fmt.Sprintf("%v", fieldValue.Interface())
		}

		// 添加到签名字符串
		signParts = append(signParts, fieldStr)
	}
	if merchantsId == 0 {
		return "", "", fmt.Errorf("未找到 merchants_id 字段")
	}

	// 拼接所有必填字段
	signString := strings.Join(signParts, "")
	return signString, strconv.FormatInt(merchantsId, 10), nil
}

// calculateHMACSHA256 计算HMAC-SHA256签名
func calculateHMACSHA256(message, secretKey string) string {
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(message))
	return hex.EncodeToString(h.Sum(nil))
}

// getMerchantAppKey 通过merchants_id查询商户的appkey
func getMerchantAppKey(merchantsID string, svcCtx *svc.ServiceContext) (string, error) {
	// 将字符串转换为int64
	id, err := strconv.ParseInt(merchantsID, 10, 64)
	if err != nil {
		return "", fmt.Errorf("商户ID格式错误: %v", err)
	}
	if id <= 0 {
		return "", fmt.Errorf("商户ID不能为空")
	}

	// 检查数据库连接
	if svcCtx.Query == nil {
		return "", fmt.Errorf("数据库连接未初始化")
	}

	// 通过id字段查询商户信息
	merchant, err := svcCtx.Query.Merchants.Where(svcCtx.Query.Merchants.ID.Eq(id)).First()
	if err != nil {
		return "", fmt.Errorf("商户不存在或查询失败: %v", err)
	}

	// 检查商户状态（0为启用，1为禁用）
	if merchant.Status != 0 {
		return "", fmt.Errorf("商户已被禁用")
	}

	return merchant.Appkey, nil
}

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorHandler 统一错误处理中间件
func ErrorHandler(ctx *fiber.Ctx, err error) error {
	// 默认错误码和消息
	code := fiber.StatusInternalServerError
	message := "Internal Server Error"

	// 根据错误类型设置不同的状态码和消息
	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
		message = e.Message
	}

	// 自定义错误处理
	if e, ok := err.(CustomError); ok {
		code = e.Code
		message = e.Message
	}

	// 返回JSON响应
	return ctx.Status(code).JSON(Response{
		Code:    code,
		Message: message,
	})
}

// CustomError 自定义错误结构
type CustomError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (e CustomError) Error() string {
	return e.Message
}

// Fail 失败响应
func Fail(c *fiber.Ctx, code int, message string) error {
	return c.Status(code).JSON(Response{
		Code:    code,
		Message: message,
	})
}
