{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 15:58:04"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 15:59:53"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 15:59:56"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 15:59:57"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 15:59:57"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:00:34"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:01:22"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:01:23"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:01:27"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:01:53"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:01:59"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:02:00"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:02:04"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:02:05"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:02:05"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:03:17"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:03:18"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:03:18"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:04:11"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:04:12"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:04:12"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:04:34"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:04:35"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:04:35"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:06:18"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:06:18"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:06:18"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:06:18"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:07:12"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:07:13"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:07:13"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:07:13"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:07:17"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:07:17"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:07:36"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:07:37"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:07:37"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:07:37"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:07:45"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:07:46"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:07:46"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:07:46"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:08:26"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:08:27"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:08:27"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:08:27"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:08:34"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:08:35"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:08:35"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:08:35"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:08:40"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:08:40"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:08:41"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:08:41"}
{"level":"info","msg":"/api/v1/messages","time":"2025-06-16 16:08:44"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:08:45"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:08:45"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:15:14"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:15:14"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:15:24"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:15:24"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:15:26"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:15:26"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:15:52"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:15:52"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:15:55"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:15:55"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:16:11"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:16:11"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:16:36"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:16:36"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:16:54"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:16:54"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:16:58"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:16:58"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:17:19"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:17:19"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:17:58"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:17:58"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:21:12"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:21:12"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:21:28"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:21:28"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:22:55"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:23:33"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:23:34"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:23:34"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:23:49"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:32:22"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:32:23"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:32:23"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:32:31"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:32:32"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:32:32"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:33:15"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:33:23"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:33:35"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:33:49"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:33:57"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:33:58"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:34:12"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:34:13"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:34:13"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:37:11"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:37:28"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:37:45"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:38:12"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:38:14"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:38:35"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:38:35"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:39:03"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:40:02"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:40:06"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:40:29"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:40:34"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:42:09"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:42:16"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:42:31"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:42:51"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:42:59"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:43:03"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:43:14"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:43:18"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:43:27"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:43:39"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:44:03"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:48:02"}
{"level":"info","msg":"Port is in use, attempting to kill process%!(EXTRA logrus.Fields=map[port:8080], logrus.Fields=map[pid:24244])","time":"2025-06-16 16:48:10"}
{"level":"info","msg":"Successfully killed process%!(EXTRA logrus.Fields=map[pid:24244])","time":"2025-06-16 16:48:11"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:48:11"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:48:18"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:48:23"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:48:24"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:48:24"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:48:54"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:48:54"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 16:48:54"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:49:05"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:49:05"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 16:49:05"}
{"level":"info","msg":"/api/v1/chain","time":"2025-06-16 16:49:07"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:49:07"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:49:44"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:49:45"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:49:46"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:49:46"}
{"level":"info","msg":"Port is in use, attempting to kill process%!(EXTRA logrus.Fields=map[port:8080], logrus.Fields=map[pid:22280])","time":"2025-06-16 16:49:57"}
{"level":"info","msg":"Successfully killed process%!(EXTRA logrus.Fields=map[pid:22280])","time":"2025-06-16 16:49:57"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:49:57"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:50:01"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:50:02"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:50:02"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:51:43"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:51:43"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:51:47"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:51:47"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:51:58"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:51:58"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:51:58"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:51:58"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:52:35"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:52:50"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:52:50"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 16:52:50"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:52:52"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:52:52"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 16:52:52"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:52:52"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:52:53"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 16:52:53"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:52:57"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 16:52:57"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 16:52:57"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:52:58"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:52:58"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:52:58"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 16:56:35"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 16:56:36"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 16:56:36"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:00:36"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:15:06"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:16:17"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:16:18"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:17:35"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:17:35"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:12"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:12"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 17:33:12"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:14"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:14"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 17:33:14"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:34"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:43"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:33:50"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:38:36"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:39:49"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:39:59"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:40:02"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:40:04"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:40:22"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:40:37"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:40:37"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:44:45"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:45:58"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:46:45"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:49:57"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:52:35"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 17:52:36"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:54:20"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:54:24"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:54:45"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:54:46"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:54:55"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:54:55"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 17:54:55"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 17:54:57"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:55:01"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:55:02"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:55:45"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:55:46"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:56:01"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:56:15"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:59:01"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:59:01"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 17:59:17"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 17:59:18"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 18:00:23"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 18:00:23"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-06-16 18:00:23"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:00:36"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:00:37"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:01:27"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:01:27"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:02:57"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:02:58"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 18:11:02"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:11:10"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:11:11"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 18:15:13"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:15:17"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:15:17"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 18:19:30"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:19:34"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:19:35"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 18:25:30"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:25:31"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 18:26:00"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 18:26:00"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 18:26:59"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 18:27:05"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 18:27:05"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:18:35"}
{"level":"info","msg":"/api/v1/login","time":"2025-06-16 19:22:55"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:22:55"}
{"level":"info","msg":"/static/avatar/admin/avatar.jpg","time":"2025-06-16 19:22:55"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:23:25"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 19:23:29"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 19:23:30"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 19:23:31"}
{"level":"info","msg":"/api/v1/orders","time":"2025-06-16 19:24:05"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:24:59"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:25:23"}
{"level":"info","msg":"/api/v1/rechargeOrdersList","time":"2025-06-16 19:25:24"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 19:25:31"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 19:26:01"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:26:04"}
{"level":"info","msg":"/api/v1/rechargeOrdersList","time":"2025-06-16 19:26:04"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-16 19:28:05"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:28:19"}
{"level":"info","msg":"/api/v1/rechargeOrdersList","time":"2025-06-16 19:28:20"}
{"level":"info","msg":"/api/v1/placeOrdersList","time":"2025-06-16 19:28:48"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:32:35"}
{"level":"info","msg":"/api/v1/placeOrdersList","time":"2025-06-16 19:32:36"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:34:40"}
{"level":"info","msg":"/api/v1/placeOrdersList","time":"2025-06-16 19:34:41"}
{"level":"info","msg":"/api/v1/placeOrdersList","time":"2025-06-16 19:36:03"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 19:44:05"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 19:44:07"}
{"level":"info","msg":"/api/v1/merchants","time":"2025-06-16 19:44:07"}
{"level":"info","msg":"/api/v1/chainList","time":"2025-06-16 19:44:10"}
{"level":"info","msg":"/api/v1/rechargeOrdersList","time":"2025-06-16 19:44:11"}
{"level":"info","msg":"/api/v1/placeOrdersList","time":"2025-06-16 19:44:11"}
{"level":"info","msg":"/api/v1/messages","time":"2025-06-16 19:44:13"}
{"level":"info","msg":"/api/v1/users","time":"2025-06-16 19:44:46"}
