{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-30 12:35:22"}
{"level":"info","msg":"签名为：c6b3d8df5b771301f4caeb15fbce1f45a97a90ed5f30cc1139e198f9a5d5ce9c","time":"2025-07-30 12:35:29"}
{"level":"info","msg":"签名为：41bd2bf47a9861427ad6a04018b993b83c7c015af52c4b211b87cf9e3f7ba7cf","time":"2025-07-30 12:35:34"}
{"level":"info","msg":"签名为：41bd2bf47a9861427ad6a04018b993b83c7c015af52c4b211b87cf9e3f7ba7cf","time":"2025-07-30 12:35:43"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-30 12:35:44"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-07-30 12:35:44"}
{"level":"info","msg":"生成新地址 - Index: 1753850144966122500, TRC: TEim5WGUo9EdtaBxmaRYB8421krQgUqzFW, ERC: 0x341d2a6E06c79d343eC646E56E481fCd4A764F12","time":"2025-07-30 12:35:44"}
{"level":"info","msg":"创建地址核心记录成功，真实ID: 11","time":"2025-07-30 12:35:45"}
{"level":"info","msg":"创建新地址成功，addressID: 11, TRC: TEim5WGUo9EdtaBxmaRYB8421krQgUqzFW, ERC: 0x341d2a6E06c79d343eC646E56E481fCd4A764F12","time":"2025-07-30 12:35:45"}
{"level":"info","msg":"发送消息: {\"address\":\"TEim5WGUo9EdtaBxmaRYB8421krQgUqzFW\",\"type\":\"addtrc\"}","time":"2025-07-30 12:35:45"}
{"level":"info","msg":"获取可用地址，TRC: TEim5WGUo9EdtaBxmaRYB8421krQgUqzFW, ERC: 0x341d2a6E06c79d343eC646E56E481fCd4A764F12, 错误: \u003cnil\u003e","time":"2025-07-30 12:35:45"}
{"level":"info","msg":"发送消息: {\"address\":\"0x341d2a6E06c79d343eC646E56E481fCd4A764F12\",\"type\":\"adderc\"}","time":"2025-07-30 12:35:45"}
{"level":"info","msg":"地址锁定成功，addressId: 11, money: 12.000000, lockTime: 300秒","time":"2025-07-30 12:35:45"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-07-30 12:36:17"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"生成新地址 - Index: 1753850186830510400, TRC: TAQZtvhtL7LoVfR2WRkGyYifiGAC7NYa7K, ERC: 0x04Cbd738687a0f64f9dC11880fcBAF019d9B1650","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"创建地址核心记录成功，真实ID: 12","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"创建新地址成功，addressID: 12, TRC: TAQZtvhtL7LoVfR2WRkGyYifiGAC7NYa7K, ERC: 0x04Cbd738687a0f64f9dC11880fcBAF019d9B1650","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"获取可用地址，TRC: TAQZtvhtL7LoVfR2WRkGyYifiGAC7NYa7K, ERC: 0x04Cbd738687a0f64f9dC11880fcBAF019d9B1650, 错误: \u003cnil\u003e","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"发送消息: {\"address\":\"TAQZtvhtL7LoVfR2WRkGyYifiGAC7NYa7K\",\"type\":\"addtrc\"}","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"发送消息: {\"address\":\"0x04Cbd738687a0f64f9dC11880fcBAF019d9B1650\",\"type\":\"adderc\"}","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"地址锁定成功，addressId: 12, money: 12.000000, lockTime: 300秒","time":"2025-07-30 12:36:26"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-30 13:06:28"}
{"level":"info","msg":"签名为：547e4f3ce3bb70f1dd125e99890c38e65a4e70baba9c21e9e2fe96f8f95d8d7e","time":"2025-07-30 13:06:51"}
{"level":"info","msg":"签名为：547e4f3ce3bb70f1dd125e99890c38e65a4e70baba9c21e9e2fe96f8f95d8d7e","time":"2025-07-30 13:06:59"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-30 14:05:33"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:06:10"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:06:18"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:11:50"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-30 14:12:42"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:12:46"}
{"level":"info","msg":"chainBalance.Balance: 4","time":"2025-07-30 14:12:46"}
{"level":"info","msg":"chainBalance.Balance: 1334.2","time":"2025-07-30 14:12:46"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:13:19"}
{"level":"info","msg":"chainBalance.Balance: 200","time":"2025-07-30 14:13:19"}
{"level":"info","msg":"chainBalance.Balance: 1334.2","time":"2025-07-30 14:13:19"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:16:56"}
{"level":"info","msg":"chainBalance.Balance: 200","time":"2025-07-30 14:16:56"}
{"level":"info","msg":"chainBalance.Balance: 20.2","time":"2025-07-30 14:16:56"}
{"level":"error","msg":"创建下发订单失败: ERROR: duplicate key value violates unique constraint \"place_order_pkey1\" (SQLSTATE 23505)","time":"2025-07-30 14:16:57"}
{"level":"info","msg":"签名为：d3d84e70672125f8865c3b8219cbea9eb5444f4f5580a9f9aef960171031979d","time":"2025-07-30 14:21:16"}
{"level":"info","msg":"chainBalance.Balance: 200","time":"2025-07-30 14:21:16"}
{"level":"info","msg":"chainBalance.Balance: 20.2","time":"2025-07-30 14:21:16"}
{"level":"info","msg":"签名为：661eb788b6726e1659f0a9e49d9783b39e5ed9c4d2ba35a67cfe38eb46bd0420","time":"2025-07-30 14:22:07"}
{"level":"info","msg":"签名为：661eb788b6726e1659f0a9e49d9783b39e5ed9c4d2ba35a67cfe38eb46bd0420","time":"2025-07-30 14:22:15"}
{"level":"info","msg":"chainBalance.Balance: 179.8","time":"2025-07-30 14:22:15"}
{"level":"info","msg":"chainBalance.Balance: 20.2","time":"2025-07-30 14:22:15"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-30 14:27:49"}
{"level":"info","msg":"签名为：c7929eac538bc919a666aa98852b8a3392d4dddba839fff1db51022198ed080e","time":"2025-07-30 14:28:06"}
{"level":"info","msg":"签名为：c7929eac538bc919a666aa98852b8a3392d4dddba839fff1db51022198ed080e","time":"2025-07-30 14:28:14"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-30 14:29:02"}
{"level":"info","msg":"签名为：c7929eac538bc919a666aa98852b8a3392d4dddba839fff1db51022198ed080e","time":"2025-07-30 14:29:04"}
