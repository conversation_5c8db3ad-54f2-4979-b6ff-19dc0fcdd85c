{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-26 18:22:51"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-26 18:22:51"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-26 18:22:51"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-26 18:22:51"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"检查IP白名单 - 当前IP: 127.0.0.1","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"当前白名单缓存内容: map[]","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"缓存大小: 0","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"上次同步时间: 0001-01-01 00:00:00 +0000 UTC","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"是否需要刷新缓存: true","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"开始刷新IP白名单缓存","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"开始从数据库查询IP白名单","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"数据库中所有白名单记录:","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"记录: ID=1, IP=***********/32, Type=1","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"记录: ID=3, IP=127.0.0.1/32, Type=2","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"记录: ID=4, IP=127.0.0.1/32, Type=1","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"从数据库查询到 1 条白名单记录","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"处理白名单记录 - IP: 127.0.0.1/32, Type: 2","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"添加IP到白名单: 127.0.0.1 (原格式: 127.0.0.1/32)","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"IP白名单缓存刷新完成 - 总计: 2 个IP","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"刷新后的白名单内容: map[127.0.0.1:true 127.0.0.1/32:true]","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"IP 127.0.0.1 在白名单中: true","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"最终白名单内容: map[127.0.0.1:true 127.0.0.1/32:true]","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"回调地址","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"aaaa #0000000100000001 - 127.0.0.1:8081 \u003c-\u003e 127.0.0.1:54426 - POST http://127.0.0.1:8081/api/v1/callBackUrl","time":"2025-08-26 18:23:26"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"检查IP白名单 - 当前IP: 127.0.0.1","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"当前白名单缓存内容: map[127.0.0.1:true 127.0.0.1/32:true]","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"缓存大小: 2","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"上次同步时间: 2025-08-26 18:23:26.1546797 +0800 CST m=+35.028778601","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"是否需要刷新缓存: false","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"IP 127.0.0.1 在白名单中: true","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"最终白名单内容: map[127.0.0.1:true 127.0.0.1/32:true]","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"回调地址","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"aaaa #0000000100000002 - 127.0.0.1:8081 \u003c-\u003e 127.0.0.1:54426 - POST http://127.0.0.1:8081/api/v1/callBackUrl","time":"2025-08-26 18:23:54"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-26 18:24:54"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-26 18:24:54"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-26 18:24:54"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-26 18:24:54"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"开始刷新IP白名单缓存","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"记录: ID=1, IP=***********/32, Type=1","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"记录: ID=3, IP=127.0.0.1/32, Type=2","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"记录: ID=4, IP=127.0.0.1/32, Type=1","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"回调地址","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"aaaa #0000000100000001 - 127.0.0.1:8081 \u003c-\u003e 127.0.0.1:54481 - POST http://127.0.0.1:8081/api/v1/callBackUrl","time":"2025-08-26 18:25:00"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-26 18:25:55"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-26 18:25:55"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-26 18:25:55"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-26 18:25:55"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:26:03"}
{"level":"info","msg":"回调地址","time":"2025-08-26 18:26:03"}
{"level":"info","msg":"aaaa #0000000100000001 - 127.0.0.1:8081 \u003c-\u003e 127.0.0.1:54535 - POST http://127.0.0.1:8081/api/v1/callBackUrl","time":"2025-08-26 18:26:03"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-26 18:26:47"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-26 18:26:47"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-26 18:26:47"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-26 18:26:47"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:26:50"}
{"level":"info","msg":"回调地址","time":"2025-08-26 18:26:50"}
{"level":"info","msg":"aaaa ","time":"2025-08-26 18:26:50"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-26 18:28:20"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-26 18:28:20"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-26 18:28:20"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-26 18:28:20"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:28:22"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:28:53"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:29:14"}
{"level":"info","msg":"IP访xxxxxxxxxxxxxxx问验证 127.0.0.1","time":"2025-08-26 18:29:25"}
