{"level":"info","msg":"用户ID: 4","time":"2025-06-15 17:01:03"}
{"client_ip":"127.0.0.1","level":"info","method":"POST","msg":"用户ID: 4","request_body":"{\"password\":\"123456\",\"username\":\"admin\"}","request_id":"85ee3c3c-5188-4a69-890a-7239b396f411","route":"/api/v1/register","time":"2025-06-15 17:01:03","user_agent":"PostmanRuntime-ApipostRuntime/1.1.0","user_id":4}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:01:43"}
{"level":"info","msg":"用户ID: 4","time":"2025-06-15 17:01:46"}
{"level":"info","method":"POST","msg":"用户ID: 4","request_body":"{\"password\":\"123456\",\"username\":\"admin\"}","request_id":"b8df517f-fa29-493e-8dbf-38501da8b472","route":"/api/v1/register","time":"2025-06-15 17:01:46","user_agent":"PostmanRuntime-ApipostRuntime/1.1.0","user_id":4}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:05:22"}
{"level":"info","msg":"用户ID: 4","time":"2025-06-15 17:05:25"}
{"level":"info","msg":"用户ID: 4","time":"2025-06-15 17:05:25","client_ip":"127.0.0.1","method":"POST","request_body":"{\"password\":\"123456\",\"username\":\"admin\"}","request_id":"1f22e242-f835-47c5-9cc3-850a6a8af062","route":"/api/v1/register","user_agent":"PostmanRuntime-ApipostRuntime/1.1.0","user_id":4}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:05:43"}
{"level":"info","msg":"用户ID: 4","time":"2025-06-15 17:05:46"}
{"level":"error","msg":"用户ID: 4","time":"2025-06-15 17:05:46","client_ip":"127.0.0.1","file":"E:/YiLing.GO/payAPI/internal/logic/auths/auth.go","line":61,"method":"POST","request_body":"{\"password\":\"123456\",\"username\":\"admin\"}","request_id":"ad8da87a-523f-46f3-a6c0-e13f456c7b99","route":"/api/v1/register","user_agent":"PostmanRuntime-ApipostRuntime/1.1.0","user_id":4}
{"level":"info","msg":"用户ID: 4","time":"2025-06-15 17:07:30"}
{"level":"error","msg":"用户ID: 4","time":"2025-06-15 17:07:30","client_ip":"127.0.0.1","file":"E:/YiLing.GO/payAPI/internal/logic/auths/auth.go","line":61,"method":"POST","request_body":"{\"password\":\"123456\",\"username\":\"admin\"}","request_id":"6b599d64-68e2-4789-b09e-84dc9ff94cc6","route":"/api/v1/register","user_agent":"PostmanRuntime-ApipostRuntime/1.1.0","user_id":4}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:16:40"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:36:25"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:50:55"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:51:13"}
{"level":"info","msg":"Port is in use, attempting to kill process%!(EXTRA logrus.Fields=map[port:8080], logrus.Fields=map[pid:19208])","time":"2025-06-15 17:54:45"}
{"level":"info","msg":"Successfully killed process%!(EXTRA logrus.Fields=map[pid:19208])","time":"2025-06-15 17:54:46"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:54:46"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:56:52"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:57:01"}
{"level":"info","msg":"Port is in use, attempting to kill process%!(EXTRA logrus.Fields=map[port:8080], logrus.Fields=map[pid:3708])","time":"2025-06-15 17:57:25"}
{"level":"info","msg":"Successfully killed process%!(EXTRA logrus.Fields=map[pid:3708])","time":"2025-06-15 17:57:25"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 17:57:26"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 18:05:33"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 18:14:48"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8080])","time":"2025-06-15 18:16:26"}
