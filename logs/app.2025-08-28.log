{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:31:58"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:31:58"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:31:58"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:31:58"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:32:22"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:32:23"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:32:23"}
{"level":"error","msg":"检查商户余额失败%!(EXTRA logrus.Fields=map[error:record not found])","time":"2025-08-28 13:32:23"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:32:42"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:32:42"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:32:42"}
{"level":"error","msg":"检查商户余额失败%!(EXTRA logrus.Fields=map[error:record not found])","time":"2025-08-28 13:32:42"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:34:15"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:34:15"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:34:15"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:34:15"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:34:19"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:34:19"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:34:19"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:37:16"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:37:16"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:37:16"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:37:16"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:37:18"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:37:18"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:37:18"}
{"level":"info","msg":"手续费检查结果: false, 手续费: 28, 配置: {TRC20 28 0.001 10 100 1000}","time":"2025-08-28 13:37:19"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:38:38"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:38:38"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:38:38"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:38:38"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:38:40"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:38:41"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:38:41"}
{"level":"info","msg":"手续费检查结果: false","time":"2025-08-28 13:38:41"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:42:56"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:42:56"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:42:56"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:42:56"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 159.6 40.4 0 0 0 2025-07-30 14:22:16.535 +0800 CST TRC20}","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 159.6 40.4 0 0 0 2025-07-30 14:22:16.535 +0800 CST TRC20}","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"手续费检查结果: false","time":"2025-08-28 13:42:59"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 159.6 40.4 100 0 0 2025-07-30 14:22:16.535 +0800 CST TRC20}","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 159.6 40.4 100 0 0 2025-07-30 14:22:16.535 +0800 CST TRC20}","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"手续费检查结果: true","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"余额和手续费检查通过%!(EXTRA logrus.Fields=map[merchants_id:1037], logrus.Fields=map[chain:TRC20], logrus.Fields=map[order_amount:12], logrus.Fields=map[estimated_fee:28])","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"生成新地址 - Index: 1756359877770324600, TRC: TXsSTkF2DcWNXABwBJ2HdAeP397freZ66d, ERC: 0xf03b81e64FDE285100109D0c50D1b598003E6c6b","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"创建地址核心记录成功，真实ID: 13","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"创建新地址成功，addressID: 13, TRC: TXsSTkF2DcWNXABwBJ2HdAeP397freZ66d, ERC: 0xf03b81e64FDE285100109D0c50D1b598003E6c6b","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"获取可用地址，TRC: TXsSTkF2DcWNXABwBJ2HdAeP397freZ66d, ERC: 0xf03b81e64FDE285100109D0c50D1b598003E6c6b, 错误: \u003cnil\u003e","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"WebSocket消息已发送%!(EXTRA logrus.Fields=map[type:addtrc])","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"WebSocket消息已发送%!(EXTRA logrus.Fields=map[type:adderc])","time":"2025-08-28 13:44:37"}
{"level":"info","msg":"地址锁定成功，addressId: 13, money: 12.000000, lockTime: 300秒","time":"2025-08-28 13:44:38"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 159.6 40.4 100 0 0 2025-07-30 14:22:16.535 +0800 CST TRC20}","time":"2025-08-28 13:44:38"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:44:38"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:44:38"}
{"level":"info","msg":"余额锁定成功%!(EXTRA logrus.Fields=map[merchants_id:1037], logrus.Fields=map[chain_name:TRC20], logrus.Fields=map[amount:12])","time":"2025-08-28 13:44:38"}
{"level":"info","msg":"订单创建成功，余额和手续费处理完成%!(EXTRA logrus.Fields=map[order_id:CT2508281344379248], logrus.Fields=map[merchants_id:1037], logrus.Fields=map[locked_amount:12], logrus.Fields=map[fee_deducted:28])","time":"2025-08-28 13:44:38"}
{"level":"error","msg":"发送ChainEye请求失败: Put \"\": unsupported protocol scheme \"\"","time":"2025-08-28 13:44:38"}
{"level":"info","msg":"ChainEye配置信息%!(EXTRA logrus.Fields=map[api_url:], logrus.Fields=map[notify_url:])","time":"2025-08-28 13:47:56"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:47:56"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:47:56"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:47:56"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:47:56"}
{"level":"error","msg":"Failed to start server failed to listen: listen tcp4 :8081: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-08-28 13:47:56"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 13:51:30"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 13:51:30"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 13:51:30"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 13:51:30"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:51:34"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:51:34"}
{"level":"info","msg":"签名为：adcdee4803a06843b1e7609d6117f8eac3bab9ba621f770176dabcfc0aef9ebc","time":"2025-08-28 13:51:34"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:51:45"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:51:45"}
{"level":"info","msg":"签名为：f7a13dba4b7f72234f4767f674eea1a427b9b2b49cd01962c9432cc6310d1ebd","time":"2025-08-28 13:51:45"}
{"level":"info","msg":"IP访问验证 127.0.0.1","time":"2025-08-28 13:51:59"}
{"level":"info","msg":"最终白名单内容: map[********:true ********/32:true 127.0.0.1:true 127.0.0.1/32:true **********:true **********/32:true]","time":"2025-08-28 13:51:59"}
{"level":"info","msg":"签名为：f7a13dba4b7f72234f4767f674eea1a427b9b2b49cd01962c9432cc6310d1ebd","time":"2025-08-28 13:51:59"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 147.6 52.4 72 0 0 2025-08-28 13:44:38.331 +0800 CST TRC20}","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 147.6 52.4 72 0 0 2025-08-28 13:44:38.331 +0800 CST TRC20}","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"手续费检查结果: true","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"余额和手续费检查通过%!(EXTRA logrus.Fields=map[merchants_id:1037], logrus.Fields=map[chain:TRC20], logrus.Fields=map[order_amount:12], logrus.Fields=map[estimated_fee:28])","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"生成新地址 - Index: 1756360320325406700, TRC: TW2EiiX1ceDdnBuELhwYhqLe2X2FsLZaV8, ERC: 0xdBF532A7B22Bd07E8a61bf62D6bBC4858c8032E5","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"创建地址核心记录成功，真实ID: 14","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"创建新地址成功，addressID: 14, TRC: TW2EiiX1ceDdnBuELhwYhqLe2X2FsLZaV8, ERC: 0xdBF532A7B22Bd07E8a61bf62D6bBC4858c8032E5","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"获取可用地址，TRC: TW2EiiX1ceDdnBuELhwYhqLe2X2FsLZaV8, ERC: 0xdBF532A7B22Bd07E8a61bf62D6bBC4858c8032E5, 错误: \u003cnil\u003e","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"WebSocket消息已发送%!(EXTRA logrus.Fields=map[type:addtrc])","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"WebSocket消息已发送%!(EXTRA logrus.Fields=map[type:adderc])","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"地址锁定成功，addressId: 14, money: 12.000000, lockTime: 300秒","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: \u0026{21 1037 147.6 52.4 72 0 0 2025-08-28 13:44:38.331 +0800 CST TRC20}","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: TRC20","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"GetMerchantBalance: 1037","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"余额锁定成功%!(EXTRA logrus.Fields=map[merchants_id:1037], logrus.Fields=map[chain_name:TRC20], logrus.Fields=map[amount:12])","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"订单创建成功，余额和手续费处理完成%!(EXTRA logrus.Fields=map[order_id:CT2508281351591057], logrus.Fields=map[merchants_id:1037], logrus.Fields=map[locked_amount:12], logrus.Fields=map[fee_deducted:28])","time":"2025-08-28 13:52:00"}
{"level":"info","msg":"准备调用ChainEye API","time":"2025-08-28 13:52:00","address":"TW2EiiX1ceDdnBuELhwYhqLe2X2FsLZaV8","api_url":"http://**************:8080/api/v1/addChain","chain_type":"TRC20","notify_url":"http://**************:8085/api/v1/callBackUrl"}
{"level":"info","msg":"ChainEye地址监控添加成功","time":"2025-08-28 13:52:01","address":"TW2EiiX1ceDdnBuELhwYhqLe2X2FsLZaV8","chain_type":"TRC20","response":"{\"code\":200,\"msg\":\"success\"}"}
{"level":"info","msg":"WebSocket服务器已初始化","time":"2025-08-28 15:00:41"}
{"level":"info","msg":"WebSocket server initialized","time":"2025-08-28 15:00:41"}
{"level":"info","msg":"WebSocket Hub 已启动","time":"2025-08-28 15:00:41"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-08-28 15:00:41"}
