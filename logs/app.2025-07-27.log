{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:16:09"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:16:42"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:18:43"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:18:46"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:19:15"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:19:19"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 查询TRC地址失败: record not found","time":"2025-07-27 13:19:20"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:21:16"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 查询ERC地址失败: record not found","time":"2025-07-27 13:21:17"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:28:30"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:28:34"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 查询ERC地址失败: record not found","time":"2025-07-27 13:28:35"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:28:55"}
{"level":"info","msg":"获取可用地址，TRC: xx12312x, ERC: 0x123123, 错误: \u003cnil\u003e","time":"2025-07-27 13:28:55"}
{"level":"error","msg":"创建订单记录失败: ERROR: duplicate key value violates unique constraint \"orders_pkey1\" (SQLSTATE 23505)","time":"2025-07-27 13:28:56"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:31:43"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:31:46"}
{"level":"info","msg":"获取可用地址，TRC: xx12312x, ERC: 0x123123, 错误: \u003cnil\u003e","time":"2025-07-27 13:31:47"}
{"level":"info","msg":"地址锁定成功，addressId: 1, money: 10.000000, lockTime: 300秒","time":"2025-07-27 13:31:47"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:37:13"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:37:17"}
{"level":"info","msg":"签名为：9a16d429e7889632483327a4e44bf98750b7893678bf0f1ada4bed000503f3e1","time":"2025-07-27 13:37:26"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:38:04"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 13:38:07"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 创建TRC地址记录失败: ERROR: duplicate key value violates unique constraint \"address_chain_asset_pkey1\" (SQLSTATE 23505)","time":"2025-07-27 13:38:07"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:38:26"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:38:26"}
{"level":"error","msg":"获取链地址失败: 查询TRC地址失败: record not found","time":"2025-07-27 13:38:27"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 查询TRC地址失败: record not found","time":"2025-07-27 13:38:27"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:40:04"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 13:40:05"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 创建TRC地址记录失败: ERROR: duplicate key value violates unique constraint \"address_chain_asset_pkey1\" (SQLSTATE 23505)","time":"2025-07-27 13:40:05"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:42:25"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:42:39"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:42:41"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 13:42:42"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 创建地址核心记录失败: ERROR: duplicate key value violates unique constraint \"address_core_pkey1\" (SQLSTATE 23505)","time":"2025-07-27 13:42:42"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 13:57:06"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:57:09"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:57:09"}
{"level":"error","msg":"获取链地址失败: 查询TRC地址失败: record not found","time":"2025-07-27 13:57:10"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 查询TRC地址失败: record not found","time":"2025-07-27 13:57:10"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:57:53"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:57:53"}
{"level":"error","msg":"获取链地址失败: 查询TRC地址失败: record not found","time":"2025-07-27 13:57:54"}
{"level":"info","msg":"获取可用地址，TRC: , ERC: , 错误: 查询TRC地址失败: record not found","time":"2025-07-27 13:57:54"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"金额：10","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"链：trc","time":"2025-07-27 13:58:30"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"警告：未配置主私钥，使用演示地址生成","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"生成演示地址 - ID: 1753595911418777400, TRC: TDemo_1753595911418777400, ERC: 0xDemo_1753595911418777400","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"发送消息: {\"address\":\"TDemo_1753595911418777400\",\"type\":\"addtrc\"}","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"发送消息: {\"address\":\"0xDemo_1753595911418777400\",\"type\":\"adderc\"}","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"创建新地址成功，addressID: 1753595911418777400, TRC: TDemo_1753595911418777400, ERC: 0xDemo_1753595911418777400","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"获取可用地址，TRC: TDemo_1753595911418777400, ERC: 0xDemo_1753595911418777400, 错误: \u003cnil\u003e","time":"2025-07-27 13:58:31"}
{"level":"info","msg":"地址锁定成功，addressId: 1753595911418777400, money: 10.000000, lockTime: 300秒","time":"2025-07-27 13:58:32"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:00:33"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:00:36"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:01:05"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"警告：未配置主私钥，使用演示地址生成","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"生成演示地址 - ID: 1753596066175332100, TRC: TDemo_1753596066175332100, ERC: 0xDemo_1753596066175332100","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"发送消息: {\"address\":\"TDemo_1753596066175332100\",\"type\":\"addtrc\"}","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"发送消息: {\"address\":\"0xDemo_1753596066175332100\",\"type\":\"adderc\"}","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"创建新地址成功，addressID: 1753596066175332100, TRC: TDemo_1753596066175332100, ERC: 0xDemo_1753596066175332100","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"获取可用地址，TRC: TDemo_1753596066175332100, ERC: 0xDemo_1753596066175332100, 错误: \u003cnil\u003e","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"地址锁定成功，addressId: 1753596066175332100, money: 10.000000, lockTime: 300秒","time":"2025-07-27 14:01:06"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:03:48"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:03:57"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:05:14"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:05:19"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:05:39"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 14:05:40"}
{"level":"info","msg":"主私钥: ","time":"2025-07-27 14:05:40"}
{"level":"info","msg":"警告：未配置主私钥，使用演示地址生成","time":"2025-07-27 14:05:40"}
{"level":"info","msg":"生成演示地址 - ID: 1753596340817789900, TRC: TDemo_1753596340817789900, ERC: 0xDemo_1753596340817789900","time":"2025-07-27 14:05:40"}
{"level":"info","msg":"发送消息: {\"address\":\"TDemo_1753596340817789900\",\"type\":\"addtrc\"}","time":"2025-07-27 14:05:41"}
{"level":"info","msg":"发送消息: {\"address\":\"0xDemo_1753596340817789900\",\"type\":\"adderc\"}","time":"2025-07-27 14:05:41"}
{"level":"info","msg":"创建新地址成功，addressID: 1753596340817789900, TRC: TDemo_1753596340817789900, ERC: 0xDemo_1753596340817789900","time":"2025-07-27 14:05:41"}
{"level":"info","msg":"获取可用地址，TRC: TDemo_1753596340817789900, ERC: 0xDemo_1753596340817789900, 错误: \u003cnil\u003e","time":"2025-07-27 14:05:41"}
{"level":"info","msg":"地址锁定成功，addressId: 1753596340817789900, money: 10.000000, lockTime: 300秒","time":"2025-07-27 14:05:41"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:11:50"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:11:54"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:12:26"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 14:12:27"}
{"level":"info","msg":"主私钥: ","time":"2025-07-27 14:12:27"}
{"level":"info","msg":"警告：未配置主私钥，使用演示地址生成","time":"2025-07-27 14:12:27"}
{"level":"info","msg":"生成演示地址 - ID: 1753596747745549100, TRC: TDemo_1753596747745549100, ERC: 0xDemo_1753596747745549100","time":"2025-07-27 14:12:27"}
{"level":"info","msg":"发送消息: {\"address\":\"TDemo_1753596747745549100\",\"type\":\"addtrc\"}","time":"2025-07-27 14:12:28"}
{"level":"info","msg":"发送消息: {\"address\":\"0xDemo_1753596747745549100\",\"type\":\"adderc\"}","time":"2025-07-27 14:12:28"}
{"level":"info","msg":"创建新地址成功，addressID: 1753596747745549100, TRC: TDemo_1753596747745549100, ERC: 0xDemo_1753596747745549100","time":"2025-07-27 14:12:28"}
{"level":"info","msg":"获取可用地址，TRC: TDemo_1753596747745549100, ERC: 0xDemo_1753596747745549100, 错误: \u003cnil\u003e","time":"2025-07-27 14:12:28"}
{"level":"info","msg":"地址锁定成功，addressId: 1753596747745549100, money: 10.000000, lockTime: 300秒","time":"2025-07-27 14:12:28"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:13:48"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:13:56"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:14:18"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 14:14:19"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-07-27 14:14:19"}
{"level":"info","msg":"生成新地址 - Index: 1753596859769086500, TRC: TMKSssYER2Wu2JifR2e2qGn4Liv68BcMB1, ERC: 0x7C7d493979475aB3b0b1b21F12A9DeF8Fbe84BF0","time":"2025-07-27 14:14:19"}
{"level":"info","msg":"发送消息: {\"address\":\"TMKSssYER2Wu2JifR2e2qGn4Liv68BcMB1\",\"type\":\"addtrc\"}","time":"2025-07-27 14:14:20"}
{"level":"info","msg":"发送消息: {\"address\":\"0x7C7d493979475aB3b0b1b21F12A9DeF8Fbe84BF0\",\"type\":\"adderc\"}","time":"2025-07-27 14:14:20"}
{"level":"info","msg":"创建新地址成功，addressID: 1753596859769086500, TRC: TMKSssYER2Wu2JifR2e2qGn4Liv68BcMB1, ERC: 0x7C7d493979475aB3b0b1b21F12A9DeF8Fbe84BF0","time":"2025-07-27 14:14:20"}
{"level":"info","msg":"获取可用地址，TRC: TMKSssYER2Wu2JifR2e2qGn4Liv68BcMB1, ERC: 0x7C7d493979475aB3b0b1b21F12A9DeF8Fbe84BF0, 错误: \u003cnil\u003e","time":"2025-07-27 14:14:20"}
{"level":"info","msg":"地址锁定成功，addressId: 1753596859769086500, money: 10.000000, lockTime: 300秒","time":"2025-07-27 14:14:20"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:19:08"}
{"level":"info","msg":"签名为：66e203a511a275984146d98fc77f0992f5e7a995471bbba0e502f42ec3a3a8d7","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"金额：10","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"商户订单号：r","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:19:11"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"生成新地址 - Index: 1753597152654112400, TRC: TJ3ZxHir7x5RxeUhawbJ7rY9BUoGvbcZ2D, ERC: 0x58942900Dca20F96aab0a900979D4F4f7015fA23","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"创建地址核心记录成功，真实ID: 8","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"发送消息: {\"address\":\"TJ3ZxHir7x5RxeUhawbJ7rY9BUoGvbcZ2D\",\"type\":\"addtrc\"}","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"发送消息: {\"address\":\"0x58942900Dca20F96aab0a900979D4F4f7015fA23\",\"type\":\"adderc\"}","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"创建新地址成功，addressID: 8, TRC: TJ3ZxHir7x5RxeUhawbJ7rY9BUoGvbcZ2D, ERC: 0x58942900Dca20F96aab0a900979D4F4f7015fA23","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"获取可用地址，TRC: TJ3ZxHir7x5RxeUhawbJ7rY9BUoGvbcZ2D, ERC: 0x58942900Dca20F96aab0a900979D4F4f7015fA23, 错误: \u003cnil\u003e","time":"2025-07-27 14:19:12"}
{"level":"info","msg":"地址锁定成功，addressId: 8, money: 10.000000, lockTime: 300秒","time":"2025-07-27 14:19:13"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:23:58"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:25:05"}
{"level":"info","msg":"签名为：a4f842d7247e08bfe98d898fd9b8eb6095ec3e17d881e24e807299af918430f7","time":"2025-07-27 14:25:17"}
{"level":"info","msg":"签名为：a4f842d7247e08bfe98d898fd9b8eb6095ec3e17d881e24e807299af918430f7","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"商户ID : 1037","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"币种 : ","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"金额：12","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"商户订单号：r2","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"回调地址：http://**************:8999/callback","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"收银台跳转地址：http://**************:8999/return_url","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"链：trc","time":"2025-07-27 14:25:27"}
{"level":"info","msg":"没有找到可用地址，创建新地址。merchantID: 1037","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"主私钥: d1562187d96d290737e9ead10346ed014fb3064e58e9812670f04594f24f17c7","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"生成新地址 - Index: 1753597528354065000, TRC: TRZ5NJuPBUrVmW9feuiCyrLpCNxg4i3aXQ, ERC: 0xAAf1a840cF90dd76cdEFd5999Abcc5e5b1f84DF5","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"创建地址核心记录成功，真实ID: 9","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"发送消息: {\"address\":\"TRZ5NJuPBUrVmW9feuiCyrLpCNxg4i3aXQ\",\"type\":\"addtrc\"}","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"发送消息: {\"address\":\"0xAAf1a840cF90dd76cdEFd5999Abcc5e5b1f84DF5\",\"type\":\"adderc\"}","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"创建新地址成功，addressID: 9, TRC: TRZ5NJuPBUrVmW9feuiCyrLpCNxg4i3aXQ, ERC: 0xAAf1a840cF90dd76cdEFd5999Abcc5e5b1f84DF5","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"获取可用地址，TRC: TRZ5NJuPBUrVmW9feuiCyrLpCNxg4i3aXQ, ERC: 0xAAf1a840cF90dd76cdEFd5999Abcc5e5b1f84DF5, 错误: \u003cnil\u003e","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"地址锁定成功，addressId: 9, money: 12.000000, lockTime: 300秒","time":"2025-07-27 14:25:28"}
{"level":"info","msg":"Server starting%!(EXTRA logrus.Fields=map[port:8081])","time":"2025-07-27 14:33:52"}
